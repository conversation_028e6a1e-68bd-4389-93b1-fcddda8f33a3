package warranty_exchange

import (
	"gorm.io/gorm"
	"marketing-app/internal/api/client/broken_screen"
	"marketing-app/internal/api/client/broken_screen/config"
	"marketing-app/internal/cache"
	"marketing-app/internal/consts"
	"marketing-app/internal/model"
	"marketing-app/internal/pkg/log"
	"marketing-app/internal/pkg/utils"
	drpMachineBuilder "marketing-app/internal/repository/machine/drp_machine/builder"
	machineBuilder "marketing-app/internal/repository/machine/machine_type/builder"
	machineTypeBuilder "marketing-app/internal/repository/machine/machine_type/builder"
	prototypeBuilder "marketing-app/internal/repository/prototype/builder"
	"marketing-app/internal/repository/warranty/exchange/builder"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	contactRepo "marketing-app/internal/repository/contact"
	userEndpointRepo "marketing-app/internal/repository/endpoint/user_endpoint"
	machineRepo "marketing-app/internal/repository/machine"
	prototypeRepo "marketing-app/internal/repository/prototype"
	exchangeRepo "marketing-app/internal/repository/warranty/exchange"
	"marketing-app/internal/repository/warranty/exchange/dto"
	"marketing-app/internal/service/warranty/warranty_exchange/entity"
)

type WarrantyExchangeService interface {
	ExchangeWarranty(c *gin.Context, param *entity.Warranty) (err error)
}

type warrantyExchange struct {
	repo               exchangeRepo.Warranty
	userEndpointRepo   userEndpointRepo.UserEndpoint
	contactRepo        contactRepo.Contact
	prototypeRepo      prototypeRepo.Prototype
	prototypeCache     cache.PrototypeCache
	machineRepo        machineRepo.Machine
	brokenScreenClient *broken_screen.BrokenScreenClient
}

func NewWarrantyExchange(
	repo exchangeRepo.Warranty,
	userEndpointRepo userEndpointRepo.UserEndpoint,
	contactRepo contactRepo.Contact,
	prototypeRepo prototypeRepo.Prototype,
	machineRepo machineRepo.Machine,
	prototypeCache cache.PrototypeCache,
) WarrantyExchangeService {
	// 初始化broken screen客户端
	cfg := config.LoadBrokenScreenConfig()
	brokenScreenClient := broken_screen.NewBrokenScreenClient(cfg)

	return &warrantyExchange{
		repo:               repo,
		userEndpointRepo:   userEndpointRepo,
		contactRepo:        contactRepo,
		prototypeRepo:      prototypeRepo,
		prototypeCache:     prototypeCache,
		machineRepo:        machineRepo,
		brokenScreenClient: brokenScreenClient,
	}
}

func (w *warrantyExchange) ExchangeWarranty(c *gin.Context, param *entity.Warranty) (err error) {
	endpoint, err := w.userEndpointRepo.GetEndpointAgencyByUid(c, param.Uid)
	if endpoint == nil {
		err = errors.New("无权限操作")
		return
	}
	warranty, newWarranty, machine, err := w.validateExchangeWarranty(c, param, endpoint.ID)
	if err != nil {
		return
	}
	customerPrice, err := w.getCustomerPrice(c, machine)
	if err != nil {
		return
	}
	oldWarranty := *warranty
	oldModelName := warranty.Model
	warranty = w.reconstructWarranty(
		param.NewBarcode,
		endpoint.ID,
		warranty,
		customerPrice,
		machine,
	)
	// 碎屏保退款
	var sn string
	brokenScreenInsurance, _ := w.CheckHasScreenInsurance(c, param.Barcode)
	if brokenScreenInsurance != nil {
		if parsedData, ok := brokenScreenInsurance.(map[string]interface{}); ok && parsedData["sn"].(string) != "" {
			sn = parsedData["sn"].(string)
			if oldModelName != machine["model"].(string) {
				err = errors.New("该产品已购买碎屏增值服务，无法更换其他型号")
				return
			}
		}
	}
	// 换货逻辑
	if newWarranty.Id != 0 { // 新条码的保卡为虚卡，更新虚卡为正常状态
		err = w.exchangeByUpdate(c, warranty, newWarranty, param, machine, endpoint.ID)
		if err != nil {
			return
		}
	} else { // 新条码的保卡不存在，则创建新保卡
		err = w.exchangeByCreate(c, warranty, param, machine, endpoint.ID)
		if err != nil {
			return
		}
	}
	// 异步任务
	numTask := 2
	errCh := make(chan error, numTask)
	var wg sync.WaitGroup
	wg.Add(numTask)
	// 添加旧保卡为虚拟保卡
	go func() {
		defer wg.Done()
		errCh <- w.addVirtualCard(c, &oldWarranty)
	}()
	// 新条码样机离库同时解除绑定
	go func() {
		defer wg.Done()
		errCh <- w.cancelPrototype(
			c,
			prototypeBuilder.NewPrototype().
				JoinMachineTypeEq("LEFT JOIN machine_type mt ON prototype.model_id = mt.model_id").
				BarcodeEq(param.NewBarcode).
				StatusIn(consts.PrototypeStatusInStock),
		)
	}()
	wg.Wait()
	close(errCh)
	// 收集协程错误
	var collectedErrs []string
	for err = range errCh {
		if err != nil {
			collectedErrs = append(collectedErrs, err.Error())
		}
	}
	if len(collectedErrs) > 0 {
		err = errors.New(strings.Join(collectedErrs, " "))
		return
	}
	// 旧条码加入样机
	returnType, err := w.createPrototype(c, param, &oldWarranty)
	if err != nil {
		return err
	}
	// 进销存同步
	err = w.machineRepo.DrpMachine(
		c,
		drpMachineBuilder.NewDrpMachine().
			BarcodeEq(param.Barcode).
			NewBarcodeEq(param.NewBarcode).
			StatusOmit(consts.MachineStatusInactive, consts.MachineStatusOutOfStock).
			OperationTypeEq(2).
			ReturnEq(returnType).
			WarrantyEq(oldWarranty.ToDrpMachineWarranty()),
	)
	// 更换碎屏保
	if brokenScreenInsurance != nil {
		if parsedInsurance, ok := brokenScreenInsurance.(map[string]interface{}); ok && parsedInsurance["sn"].(string) != "" {
			exchangeBroken, _ := w.ExchangeScreenInsurance(c, param.Barcode, param.NewBarcode, sn)
			if exchangeBroken == nil {
				log.Warn("换货成功，碎屏保更换失败")
			}
		}
	}
	return nil
}

// CheckHasScreenInsurance 调用repair api获取条码碎屏险信息
func (w *warrantyExchange) CheckHasScreenInsurance(c *gin.Context, barcode string) (interface{}, error) {
	return w.brokenScreenClient.CheckHasScreenInsurance(c, barcode)
}

func (w *warrantyExchange) ExchangeScreenInsurance(c *gin.Context, barcode string, newBarcode string, orderSn string) (interface{}, error) {
	return w.brokenScreenClient.ScreenInsuranceExchange(c, barcode, newBarcode, orderSn)
}

func (w *warrantyExchange) getCustomerPrice(c *gin.Context, machine map[string]interface{}) (float64, error) {
	machineModelID, _ := strconv.Atoi(machine["model_id"].(string))
	machineType, err := w.machineRepo.GetMachineType(
		c,
		machineBuilder.NewMachineType().ModelIdEq(machineModelID).VisibilityEq(1),
	)
	if err != nil || machineType.CustomerPrice == 0 {
		err = errors.New("无对应机型价格信息")
		return 0, err
	}
	return machineType.CustomerPrice, nil
}

func (w *warrantyExchange) validateExchangeWarranty(c *gin.Context, param *entity.Warranty, endpointId int) (oldWarranty, newWarranty *dto.Warranty, machine map[string]interface{}, err error) {
	if !utils.IsBarcode(param.Barcode) || !utils.IsBarcode(param.NewBarcode) {
		err = errors.New("条码格式错误")
		return
	}
	oldWarranty, err = w.repo.Get(c, builder.NewWarranty().BarcodeEq(param.Barcode).StatusIn(consts.WarrantyStatusActive))
	if err != nil {
		err = errors.New("旧机器电子保卡不存在")
		return
	}
	if oldWarranty.Endpoint != endpointId {
		err = errors.New("此保卡非此终端录入，无权限操作")
		return
	}
	if oldWarranty.BuyDate.Add(30 * 24 * time.Hour).Before(*param.ExchangeDate) {
		err = errors.New("已经超出换货有效期")
		return
	}
	if oldWarranty.Type == consts.WarrantyTypeFrozen {
		err = errors.New("此保卡已冻结，请联系客服部")
		return
	}
	machine, err = utils.CheckMachine(utils.CheckMachineParams{
		Barcode: param.NewBarcode,
	})
	if err != nil || machine == nil {
		err = errors.New("无效的新机器条码")
		return
	}
	extBarcodeNum, _ := strconv.Atoi(machine["ext_barcode_count"].(string))
	if extBarcodeNum < 0 {
		err = errors.New("此新机器条码为副机条码，不可进行换机，请输入对应的新机器主机条码")
		return
	}
	newWarranty, err = w.repo.Get(
		c,
		builder.NewWarranty().
			JoinMachineType("LEFT JOIN machine_type mt ON warranty.model_id = mt.model_id").
			BarcodeOrExtBarcodeEq(param.NewBarcode, param.NewBarcode).
			StatusIn(consts.WarrantyStatusActive, consts.WarrantyStatusVirtual),
	)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	if newWarranty.Status == consts.WarrantyStatusActive {
		err = errors.New("新机器电子保卡已存在")
		return
	}
	// 查询新条码样机信息
	prototypeInfo, _ := w.prototypeRepo.GetPrototypeMachineTypeConfig(
		c,
		prototypeBuilder.NewPrototype().
			JoinPrototypeConfigEq("LEFT JOIN prototype_config pc ON prototype.model_id = pc.model_id").
			JoinMachineTypeEq("LEFT JOIN machine_type mt ON prototype.model_id = mt.model_id").
			BarcodeEq(param.NewBarcode).
			StatusIn(consts.WarrantyStatusActive),
	)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	if prototypeInfo != nil && prototypeInfo.Type == consts.PrototypeTypeDemo && prototypeInfo.Discontinued == 0 {
		err = errors.New("演示样机在库不允许录入保卡")
		return
	}
	// 暂只能通过主条码换机，不校验副机逻辑
	return oldWarranty, newWarranty, machine, nil
}

func (w *warrantyExchange) reconstructWarranty(newBarcode string, endpointId int, warranty *dto.Warranty, customerPrice float64, machine map[string]interface{}) *dto.Warranty {
	modelID, _ := strconv.Atoi(machine["model_id"].(string))
	productTime, _ := time.Parse(time.DateTime, machine["product_date"].(string))
	warranty.CustomerPrice = customerPrice
	warranty.ModelId = modelID
	warranty.Model = machine["model"].(string)
	warranty.ProductDate = &productTime
	warranty.Barcode = newBarcode
	warranty.Imei = machine["imei1"].(string)
	warranty.Endpoint = endpointId
	return warranty
}

func (w *warrantyExchange) exchangeByUpdate(c *gin.Context, warranty *dto.Warranty, newWarranty *dto.Warranty, param *entity.Warranty, machine map[string]interface{}, endpointId int) (err error) {
	return w.repo.Update(c, warranty, newWarranty, param, machine, endpointId)
}

func (w *warrantyExchange) exchangeByCreate(c *gin.Context, warranty *dto.Warranty, param *entity.Warranty, machine map[string]interface{}, endpointId int) (err error) {
	return w.repo.Create(c, warranty, param, machine, endpointId)
}

func (w *warrantyExchange) addVirtualCard(c *gin.Context, warranty *dto.Warranty) (err error) {
	now := time.Now()
	virtual := consts.WarrantyStatusVirtual
	return w.repo.CreateVirtualCard(
		c,
		builder.NewWarranty().Omits("created_at", "buy_date"),
		&model.Warranty{
			Number:        warranty.Number,
			Barcode:       warranty.Barcode,
			Imei:          warranty.Imei,
			Status:        &virtual,
			ModelID:       warranty.ModelId,
			Model:         warranty.Model,
			CustomerPrice: warranty.CustomerPrice,
			ProductDate:   warranty.ProductDate,
			CreatedAtNew:  &now,
		},
	)
}

func (w *warrantyExchange) cancelPrototype(c *gin.Context, query *prototypeBuilder.Prototype) error {
	prototype, err := w.prototypeRepo.GetPrototypeMachineType(c, query)
	if err != nil {
		return err
	}
	if prototype.ID == 0 {
		log.Warn("无对应条码的样机数据")
		return nil
	}
	if prototype.Status != consts.PrototypeStatusInStock {
		err = errors.New("非样机")
		return err
	}
	// 删除样机数据
	affected, err := w.prototypeRepo.Update(
		c,
		prototypeBuilder.NewPrototype().BarcodeEq(query.Barcode).NumberEq(query.Number),
		map[string]interface{}{
			"status":     consts.PrototypeStatusOutStock,
			"removed_at": time.Now().Format(time.DateTime),
			"updated_at": time.Now().Format(time.DateTime),
		})
	// 更新样机状态
	if err != nil {
		err = errors.Wrap(err, "更新样机状态失败")
		return err
	}
	if affected == 0 {
		log.Warn("未找到样机数据")
		return nil
	}
	// 删除缓存
	err = w.prototypeCache.Del(c, prototype.Number)
	if err != nil {
		err = errors.New("删除缓存失败")
		return err
	}
	if prototype.CategoryID == consts.ModelCategoryStudentPad {
		// TODO:取消绑定（app端）
	}
	return nil
}

func (w *warrantyExchange) createPrototype(c *gin.Context, param *entity.Warranty, warranty *dto.Warranty) (returnType int, err error) {
	// 检查机型是否可作为样机
	_, err = w.machineRepo.GetMachineType(c, machineTypeBuilder.NewMachineType().PrototypeStatusEq(consts.PrototypeStatusInStock).ModelIdEq(warranty.ModelId))
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("获取机器类型失败")
		return
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return 3, nil
	}
	// 查询样机信息
	prototypeInfo, err := w.prototypeRepo.GetPrototypeMachineTypeConfig(
		c,
		prototypeBuilder.NewPrototype().
			JoinPrototypeConfigEq("LEFT JOIN prototype_config pc ON prototype.model_id = pc.model_id").
			JoinMachineTypeEq("LEFT JOIN machine_type mt ON prototype.model_id = mt.model_id").
			BarcodeEq(param.Barcode). // 判断是否是演示样机
			StatusIn(consts.WarrantyStatusActive),
	)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	if prototypeInfo != nil && prototypeInfo.Status == consts.PrototypeStatusInStock {
		return 1, nil
	}
	// 未找到样机时添加样机
	err = w.prototypeRepo.Create(c,
		&model.Prototype{
			Number:   warranty.Number,
			Barcode:  param.Barcode,
			Imei:     warranty.Imei,
			Status:   consts.PrototypeStatusInStock,
			Endpoint: warranty.Endpoint,
			Model:    warranty.Model,
			ModelID:  warranty.ModelId,
			UserID:   param.Uid,
			Type:     consts.PrototypeTypeExchange,
		},
	)
	if err != nil {
		return
	}
	// 添加样机缓存
	err = w.prototypeCache.Set(c, warranty.Number)
	if err != nil {
		err = errors.Wrap(err, "添加缓存失败")
		return
	}
	return 2, nil
}
