package model

import (
	"gorm.io/gorm"
	"time"
)

type Warranty struct {
	ID                  int        `gorm:"primaryKey;autoIncrement" json:"id"`
	ModelID             int        `gorm:"column:model_id" json:"model_id"`
	Model               string     `gorm:"column:model" json:"model"`
	Barcode             string     `gorm:"column:barcode" json:"barcode"`
	Number              string     `gorm:"column:number" json:"number"`
	Imei                string     `gorm:"column:imei" json:"imei"`
	ExtBarcode          string     `gorm:"column:ext_barcode" json:"ext_barcode"`
	Source              int        `gorm:"column:source" json:"source"`
	EndpointID          int        `gorm:"column:endpoint" json:"endpoint_id"`
	BuyDate             *time.Time `gorm:"column:buy_date" json:"buy_date"`
	WarrantyPeriod      *time.Time `gorm:"column:warranty_period;default:" json:"warranty_period"`
	ProductDate         *time.Time `gorm:"column:product_date" json:"product_date"`
	CreatedAtNew        *time.Time `gorm:"column:created_at_new" json:"created_at_new"`
	CreatedAt           *time.Time `gorm:"column:created_at" json:"created_at"`
	ActivatedAtOld      *time.Time `gorm:"column:activated_at_old" json:"activated_at_old"`
	ActivatedID         int        `gorm:"column:activated_id" json:"activated_id"`
	DeletedAt           *time.Time `gorm:"column:deleted_at" json:"deleted_at"`
	Realsale            int        `gorm:"column:realsale" json:"realsale"` // 1-是，0-否
	Assessment          *int       `gorm:"force;column:assessment;default:null" json:"assessment"`
	Status              *int       `gorm:"column:status;default:null" json:"status"` // -1-虚卡翻新，0-虚卡，1-正常，2-换机，3-退机，4-其他，5-翻新
	SalesmanID          int        `gorm:"column:salesman_id" json:"salesman_id"`
	Salesman            string     `gorm:"column:salesman" json:"salesman"`
	CustomerPrice       float64    `gorm:"column:customer_price" json:"customer_price"`
	CustomerName        string     `gorm:"column:customer_name" json:"customer_name"`
	CustomerSex         string     `gorm:"column:customer_sex" json:"customer_sex"` // m-男，f-女
	CustomerPhone       string     `gorm:"column:customer_phone" json:"customer_phone"`
	CustomerAddr        string     `gorm:"column:customer_addr" json:"customer_addr"`
	StudentUID          int        `gorm:"column:student_uid" json:"student_uid"`
	StudentName         string     `gorm:"column:student_name" json:"student_name"`
	StudentSex          string     `gorm:"column:student_sex" json:"student_sex"`
	StudentSchoolID     int        `gorm:"column:student_school_id" json:"student_school_id"`
	StudentSchool       string     `gorm:"column:student_school" json:"student_school"`
	StudentSchoolAdcode int        `gorm:"column:student_school_adcode" json:"student_school_adcode"`
	StudentGrade        string     `gorm:"column:student_grade" json:"student_grade"`
	StudentBirthday     *time.Time `gorm:"column:student_birthday" json:"student_birthday"`
	CallBack            int        `gorm:"column:call_back" json:"call_back"`
	PurchaseWay         string     `gorm:"column:purchase_way" json:"purchase_way"`
	Recommender         string     `gorm:"column:recommender" json:"recommender"`
	RecommenderPhone    string     `gorm:"column:recommender_phone" json:"recommender_phone"`
	State               *int       `gorm:"column:state;default:null" json:"state"`
	InBillDate          *time.Time `gorm:"column:in_bill_date" json:"in_bill_date"`
	OutBillDate         *time.Time `gorm:"column:out_bill_date" json:"out_bill_date"`
	OutCustCode         string     `gorm:"column:out_cust_code" json:"out_cust_code"`
	Type                int        `gorm:"column:type;default:1" json:"type"` // 1正常，-1-已冻结
	Prototype           int        `gorm:"column:prototype" json:"prototype"` // 0为非样机  1为样机
	UpdatedAt           *time.Time `gorm:"column:updated_at" json:"updated_at"`
	EcCreatedAt         *time.Time `gorm:"column:ec_created_at" json:"ec_created_at"`
	EcType              int        `gorm:"column:ec_type" json:"ec_type"`
	EcEndpoint          int        `gorm:"column:ec_endpoint" json:"ec_endpoint"`
	Channel             string     `gorm:"column:channel" json:"channel"`
	ActivatedStatus     int        `gorm:"column:activated_status" json:"activated_status"`
	ActivatedAt         *time.Time `gorm:"column:activated_at" json:"activated_at"`
	LngActivated        string     `gorm:"column:lng_activated" json:"lng_activated"`
	LatActivated        string     `gorm:"column:lat_activated" json:"lat_activated"`
	Lng                 string     `gorm:"column:lng;default:0" json:"lng"`
	Lat                 string     `gorm:"column:lat;default:0" json:"lat"`
}

func (Warranty) TableName() string {
	return "warranty"
}

func (w Warranty) BeforeCreate(tx *gorm.DB) (err error) {
	if w.Assessment == nil {
		var zero int
		w.Assessment = &zero
	}
	if w.State == nil {
		one := 1
		w.State = &one
	}
	return
}
