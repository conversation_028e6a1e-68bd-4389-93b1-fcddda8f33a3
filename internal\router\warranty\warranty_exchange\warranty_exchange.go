package warranty_exchange

import (
	"github.com/gin-gonic/gin"
	"marketing-app/internal/cache"
	"marketing-app/internal/handler/warranty/warranty_exchange"
	"marketing-app/internal/pkg/db"
	contactRepo "marketing-app/internal/repository/contact"
	userEndpointRepo "marketing-app/internal/repository/endpoint/user_endpoint"
	machineRepo "marketing-app/internal/repository/machine"
	prototypeRepo "marketing-app/internal/repository/prototype"
	"marketing-app/internal/repository/warranty/exchange"
	exchangeService "marketing-app/internal/service/warranty/warranty_exchange"
)

type WarrantyExchangeRouter struct {
	warrantyExchangeHandler warranty_exchange.WarrantyExchangeHandler
}

func NewWarrantyExchangeRouter() *WarrantyExchangeRouter {
	database, _ := db.GetDB()
	repo := exchange.NewWarranty(database)
	ueRepo := userEndpointRepo.NewUserEndpoint(database)
	ctRepo := contactRepo.NewContact(database)
	ptRepo := prototypeRepo.NewPrototypeRepo(database)
	mtRepo := machineRepo.NewMachine(database)
	ptCache := cache.NewPrototypeCache()
	warrantyExchangeSvc := exchangeService.NewWarrantyExchange(repo, ueRepo, ctRepo, ptRepo, mtRepo, ptCache)
	return &WarrantyExchangeRouter{
		warrantyExchangeHandler: warranty_exchange.NewWarrantyHandler(warrantyExchangeSvc),
	}
}

func (w *WarrantyExchangeRouter) Register(r *gin.RouterGroup) {
	g := r.Group("/warranty_exchange")
	{
		g.POST("/exchange", w.warrantyExchangeHandler.ExchangeWarranty)
	}
}
