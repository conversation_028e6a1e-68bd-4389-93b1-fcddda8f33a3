package warranty_entry

import (
	"github.com/gin-gonic/gin"
	"marketing-app/internal/cache"
	entryHandler "marketing-app/internal/handler/warranty/warranty_entry"
	"marketing-app/internal/pkg/db"
	commonRepo "marketing-app/internal/repository/common"
	contactRepo "marketing-app/internal/repository/contact"
	endpointRepo "marketing-app/internal/repository/endpoint/endpoint"
	userEndpointRepo "marketing-app/internal/repository/endpoint/user_endpoint"
	machineRepo "marketing-app/internal/repository/machine"
	prototypeRepo "marketing-app/internal/repository/prototype"
	entryRepo "marketing-app/internal/repository/warranty/entry"
	entryService "marketing-app/internal/service/warranty/warranty_entry"
)

type WarrantyEntryRouter struct {
	warrantyEntryHandler entryHandler.WarrantyEntryHandler
}

func NewWarrantyEntryRouter() *WarrantyEntryRouter {
	database, _ := db.GetDB()
	repo := entryRepo.NewWarranty(database)
	mesDevicesRepo := commonRepo.NewMesDevices(database)
	mtRepo := machineRepo.NewMachine(database)
	ueRepo := userEndpointRepo.NewUserEndpoint(database)
	ptRepo := prototypeRepo.NewPrototypeRepo(database)
	ptCache := cache.NewPrototypeCache()
	epRepo := endpointRepo.NewEndpoint(database)
	ctRepo := contactRepo.NewContact(database)
	warrantyEntrySvc := entryService.NewWarrantyEntry(repo, ueRepo, ptRepo, mtRepo, mesDevicesRepo, epRepo, ctRepo, ptCache)
	return &WarrantyEntryRouter{
		warrantyEntryHandler: entryHandler.NewWarrantyHandler(warrantyEntrySvc),
	}
}

func (w *WarrantyEntryRouter) Register(r *gin.RouterGroup) {
	g := r.Group("warranty_entry")
	{
		g.POST("/entry", w.warrantyEntryHandler.CreateWarranty)          // 保卡录入
		g.GET("/barcode/:barcode", w.warrantyEntryHandler.CheckWarranty) // 保卡查询
	}
}
