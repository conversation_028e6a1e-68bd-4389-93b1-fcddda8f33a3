package model

import "time"

// EndpointSetting 终端app功能设置表
type EndpointSetting struct {
	ID                      int        `gorm:"primaryKey;column:id;type:int(10)unsigned;not null"`
	EndpointID              int        `gorm:"uniqueIndex;column:endpoint_id;type:int(10)unsigned;not null"`
	BirthdayRemindFrequency string     `gorm:"column:birthday_remind_frequency;type:enum('daily','weekly','monthly');not null;default:'weekly';comment:'生日提醒频率'"`
	ReturnVisitRemindDays   string     `gorm:"column:return_visit_remind_days;type:varchar(10);not null;default:'7';comment:'待回访客户购机后多少天提醒回访，如7,14,16,最多三个时间'"`
	CreatedAt               *time.Time `gorm:"column:created_at;type:timestamp;not null;default:'0000-00-00 00:00:00'"`
	UpdatedAt               *time.Time `gorm:"column:updated_at;type:timestamp;not null;default:'0000-00-00 00:00:00'"`
	PrototypeLimit          int        `gorm:"column:prototype_limit;type:tinyint(3);not null;default:-1;comment:'在库样机上限'"`
	PrototypeFrequencyLimit int        `gorm:"column:prototype_frequency_limit;type:tinyint(3) unsigned;not null;default:2;comment:'每月录入样机频次'"`
	ReturnVisit             string     `gorm:"column:return_visit;type:varchar(255);not null;default:'';comment:'客户回访设置'"`
	PaBannerActivities      string     `gorm:"column:pa_banner_activities;type:varchar(150);not null;comment:'家长助手banner位活动列表'"`
	PaServiceActivities     string     `gorm:"column:pa_service_activities;type:varchar(150);not null;comment:'家长助手服务位活动列表'"`
}

// TableName 指定表名，GORM 默认会使用结构体名称的复数形式
func (EndpointSetting) TableName() string {
	return "endpoint_setting"
}
