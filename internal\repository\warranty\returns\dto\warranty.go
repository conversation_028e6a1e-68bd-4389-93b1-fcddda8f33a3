package dto

import (
	drpMachineDto "marketing-app/internal/repository/machine/drp_machine/dto"
	"time"
)

type Warranty struct {
	Id            int
	Type          int
	BuyDate       *time.Time
	Endpoint      int
	CustomerPhone string
	Barcode       string
	Number        string
	Imei          string
	Model         string
	ModelId       int
	CustomerPrice float64
	ProductDate   *time.Time
}

func (w *Warranty) ToDrpMachineWarranty() *drpMachineDto.Warranty {
	return &drpMachineDto.Warranty{
		Id:            w.Id,
		Type:          w.Type,
		BuyDate:       w.BuyDate,
		Endpoint:      w.Endpoint,
		CustomerPhone: w.CustomerPhone,
		Barcode:       w.Barcode,
		Number:        w.Number,
		Imei:          w.Imei,
		Model:         w.Model,
		ModelId:       w.ModelId,
		CustomerPrice: w.CustomerPrice,
		ProductDate:   w.ProductDate,
	}
}
