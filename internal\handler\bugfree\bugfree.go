package bugfree

import (
	"marketing-app/internal/handler"
	"marketing-app/internal/handler/bugfree/dto"
	"marketing-app/internal/pkg/convertor/bugfree_convertor"
	"marketing-app/internal/pkg/log"
	"marketing-app/internal/pkg/oss"
	"marketing-app/internal/pkg/utils"
	"marketing-app/internal/router/bugfree/client"
	service "marketing-app/internal/service/bugfree"
	"mime/multipart"
	"strconv"
	"strings"

	"go.uber.org/zap"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

type Bugfree interface {
	GetBugfreeUser(c *gin.Context)
	GetBugFreeCategory(c *gin.Context)
	GetBugfreeSearch(c *gin.Context)
	GetBugfreeInfo(c *gin.Context)
	GetBugfreeReplies(c *gin.Context)
	PostBugfreeReply(c *gin.Context)
	PostBugfreeDuplicateReply(c *gin.Context)
	PostBugfreeRepeat(c *gin.Context)
	GetTextbookResource(c *gin.Context)
	GetSubjectGrade(c *gin.Context)
	GetBugfreeLabel(c *gin.Context)
	GetBugfreeNotice(c *gin.Context)
	PostBugfreeEvaluations(c *gin.Context)
	PostBugfreeAddV4(c *gin.Context)
	PostBugfreeRepairAddV2(c *gin.Context)
	PostBugfreeUploadFile(c *gin.Context)
}

type bugfree struct {
	bugfreeSvc service.BugfreeSvc
}

func NewBugfreeHandler(svc service.BugfreeSvc) Bugfree {
	return &bugfree{bugfreeSvc: svc}
}

func (b *bugfree) GetBugfreeUser(c *gin.Context) {
	var (
		req  client.GetBugfreeUserRequest
		err  error
		resp *dto.BugfreeUserResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}
	// 测试环境下通过json直接传uid，发布模式下通过传token并且经过auth后从context中获取，经过auth则说明已经通过登录的校验，后续只执行获取user的操作
	if gin.Mode() == gin.ReleaseMode {
		req.Uid = c.MustGet("uid").(int)
	}

	data, err := b.bugfreeSvc.GetBugfreeUser(
		c,
		bugfree_convertor.NewBugfreeUserConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}

	if data == nil {
		err = errors.New("未找到数据")
		return
	}

	resp = &dto.BugfreeUserResp{
		Data: data,
	}
}

func (b *bugfree) GetBugFreeCategory(c *gin.Context) {
	var (
		req client.BugfreeCategoryRequest
		err error
	)
	resp := dto.BugFreeCategoryResp{Data: map[string]interface{}{}}
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}
	// 测试环境下通过json直接传uid，发布模式下通过传token并且经过auth后从context中获取，经过auth则说明已经通过登录的校验，后续只执行获取user的操作
	if gin.Mode() == gin.ReleaseMode {
		req.Uid = c.MustGet("uid").(int)
	}

	data, machine, err := b.bugfreeSvc.GetBugfreeCategory(
		c,
		bugfree_convertor.NewBugfreeCategoryConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}
	resp.Data["products"] = data
	resp.Data["machine_type"] = machine.Titles
	resp.Data["machine_type2"] = machine.Detail
}

// GetBugfreeSearch 搜索问题列表，重构了python的实现，企微中的该api在api-pad-feedback-test.readboy.com中 TODO: 当前如果想成功获取外部api返回的结果还需要传app_id等信息
func (b *bugfree) GetBugfreeSearch(c *gin.Context) {
	var (
		req  client.BugfreeSearchRequest
		err  error
		resp *dto.BugfreeSearchResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}
	// 测试环境下通过json直接传uid，发布模式下通过传token并且经过auth后从context中获取，经过auth则说明已经通过登录的校验，后续只执行获取user的操作
	if gin.Mode() == gin.ReleaseMode {
		req.Uid = c.MustGet("uid").(int)
	}

	data, err := b.bugfreeSvc.GetBugfreeSearch(
		c,
		bugfree_convertor.NewBugfreeSearchConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}

	if data == nil {
		err = errors.New("未找到数据")
		return
	}

	resp = &dto.BugfreeSearchResp{
		Data: data,
	}
}

// GetBugfreeInfo 获取问题详情，重构了python的实现，TODO: 当前如果想成功获取外部api返回的结果还需要传app_id等信息
func (b *bugfree) GetBugfreeInfo(c *gin.Context) {
	var (
		req  client.BugfreeInfoRequest
		err  error
		resp *dto.BugfreeInfoResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}
	// 测试环境下通过json直接传uid，发布模式下通过传token并且经过auth后从context中获取，经过auth则说明已经通过登录的校验，后续只执行获取user的操作
	if gin.Mode() == gin.ReleaseMode {
		req.Uid = c.MustGet("uid").(int)
	}

	data, err := b.bugfreeSvc.GetBugfreeInfo(
		c,
		bugfree_convertor.NewBugfreeInfoConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}

	if data == nil {
		err = errors.New("未找到数据")
		return
	}

	resp = &dto.BugfreeInfoResp{
		Data: data,
	}
}

// GetBugfreeReplies 获取问题回复，重构了python的实现，TODO: 当前如果想成功获取外部api返回的结果还需要传app_id等信息
func (b *bugfree) GetBugfreeReplies(c *gin.Context) {
	var (
		req  client.BugfreeRepliesRequest
		err  error
		resp *dto.BugfreeRepliesResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}
	// 测试环境下通过json直接传uid，发布模式下通过传token并且经过auth后从context中获取，经过auth则说明已经通过登录的校验，后续只执行获取user的操作
	if gin.Mode() == gin.ReleaseMode {
		req.Uid = c.MustGet("uid").(int)
	}

	data, err := b.bugfreeSvc.GetBugfreeReplies(
		c,
		bugfree_convertor.NewBugfreeRepliesConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}

	if data == nil {
		err = errors.New("未找到数据")
		return
	}

	resp = &dto.BugfreeRepliesResp{
		Data: data,
	}
}

// PostBugfreeReply 提交回复，重构了python的实现，TODO: 当前如果想成功获取外部api返回的结果还需要传app_id等信息
func (b *bugfree) PostBugfreeReply(c *gin.Context) {
	var (
		req  client.BugfreeReplyRequest
		err  error
		resp *dto.BugfreeReplyResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}
	if gin.Mode() == gin.ReleaseMode {
		req.Uid = c.MustGet("uid").(int)
	}

	err = b.bugfreeSvc.PostBugfreeReply(
		c,
		bugfree_convertor.NewBugfreeReplyConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}
	resp = &dto.BugfreeReplyResp{Data: true}
}

// PostBugfreeDuplicateReply 标记问题重复并回复，重构了python的实现，TODO: 当前如果想成功获取外部api返回的结果还需要传app_id等信息
func (b *bugfree) PostBugfreeDuplicateReply(c *gin.Context) {
	var (
		req  client.BugfreeDuplicateReplyRequest
		err  error
		resp *dto.BugfreeDuplicateReplyResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}
	if gin.Mode() == gin.ReleaseMode {
		req.Uid = c.MustGet("uid").(int)
	}

	// 验证必需字段
	if req.Id == 0 {
		err = errors.New("bug反馈ID不能为空")
		return
	}
	if req.Username == "" {
		err = errors.New("用户名不能为空")
		return
	}
	if req.PhoneNumber == "" {
		err = errors.New("手机号码不能为空")
		return
	}

	data, err := b.bugfreeSvc.PostBugfreeDuplicateReply(
		c,
		bugfree_convertor.NewBugfreeDuplicateReplyConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}

	resp = &dto.BugfreeDuplicateReplyResp{
		Data: data,
	}
}

// PostBugfreeRepeat 问题我也遇到，重构了python的实现，TODO: 当前如果想成功获取外部api返回的结果还需要传app_id等信息
func (b *bugfree) PostBugfreeRepeat(c *gin.Context) {
	var (
		req  client.BugfreeRepeatRequest
		err  error
		resp *dto.BugfreeRepeatResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}

	// 验证必需字段
	if req.Id == 0 {
		err = errors.New("bug反馈ID不能为空")
		return
	}
	if req.UID == 0 {
		err = errors.New("用户ID不能为空")
		return
	}

	data, err := b.bugfreeSvc.PostBugfreeRepeat(
		c,
		bugfree_convertor.NewBugfreeRepeatConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}

	resp = &dto.BugfreeRepeatResp{
		Data: data,
	}
}

// GetTextbookResource 获取教材资源标签，重构了python的实现
func (b *bugfree) GetTextbookResource(c *gin.Context) {
	var (
		err  error
		resp *dto.TextbookResourceResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	data, err := b.bugfreeSvc.GetTextbookResource(c)
	if err != nil {
		return
	}

	if data == nil {
		err = errors.New("未找到数据")
		return
	}

	resp = &dto.TextbookResourceResp{
		Data: data,
	}
}

// GetSubjectGrade 获取学科年级信息，重构了python的实现
func (b *bugfree) GetSubjectGrade(c *gin.Context) {
	var (
		err  error
		resp *dto.SubjectGradeResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	data, err := b.bugfreeSvc.GetSubjectGrade(c)
	if err != nil {
		return
	}

	resp = &dto.SubjectGradeResp{
		Data: data,
	}
}

// GetBugfreeLabel 获取问题反馈分类标签，重构了python的实现
func (b *bugfree) GetBugfreeLabel(c *gin.Context) {
	var (
		req  client.BugfreeLabelRequest
		err  error
		resp *dto.BugfreeLabelResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定查询参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}
	log.Info("req", zap.Any("req", req))
	// 参数验证：必须提供 category_id 或 category_key 中的至少一个
	if req.CategoryId == 0 && req.CategoryKey == "" {
		err = errors.New("标签类型不能为空")
		return
	}

	data, err := b.bugfreeSvc.GetBugfreeLabel(c, req.CategoryId, req.CategoryKey)
	if err != nil {
		return
	}

	if data == nil {
		err = errors.New("未找到数据")
		return
	}

	resp = &dto.BugfreeLabelResp{
		Data: data,
	}
}

// GetBugfreeNotice 获取反馈须知，重构了python的实现，TODO: 当前版本是直接把数据库的内容取出，后续再添加缓存
func (b *bugfree) GetBugfreeNotice(c *gin.Context) {
	var (
		err  error
		resp *dto.BugfreeNoticeResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	data, err := b.bugfreeSvc.GetBugfreeNotice(c)
	if err != nil {
		return
	}

	if data == nil {
		err = errors.New("未找到数据")
		return
	}

	resp = &dto.BugfreeNoticeResp{
		Data: data,
	}
}

// PostBugfreeEvaluations 提交问题反馈评价，重构了python的实现，TODO: 当前如果想成功获取外部api返回的结果还需要传app_id等信息
func (b *bugfree) PostBugfreeEvaluations(c *gin.Context) {
	var (
		req  client.BugfreeEvaluationsRequest
		err  error
		resp *dto.BugfreeEvaluationsResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}

	// 参数验证
	if req.BugId == 0 {
		err = errors.New("id不能为空")
		return
	}
	if req.Satisfied != 0 && req.Satisfied != 1 {
		err = errors.New("请选择是否满意")
		return
	}
	if req.Satisfied == 0 && req.Remark == "" {
		err = errors.New("请选择备注")
		return
	}
	if req.Satisfied == 0 && req.Activation != 0 && req.Activation != 1 {
		err = errors.New("请选择是否激活")
		return
	}

	// 调用service层
	err = b.bugfreeSvc.PostBugfreeEvaluations(
		c,
		bugfree_convertor.NewBugfreeEvaluationsConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}

	// 成功时返回空对象
	resp = &dto.BugfreeEvaluationsResp{
		Data: map[string]interface{}{},
	}
}

// PostBugfreeAddV4 提交问题反馈（第四版本），重构了python的实现，TODO: 当前如果想成功获取外部api返回的结果还需要传app_id等信息
func (b *bugfree) PostBugfreeAddV4(c *gin.Context) {
	var (
		req  client.BugfreeAddV4Request
		err  error
		resp *dto.BugfreeAddV4Resp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}

	// 在企微中不确定是否还需要检查设备版本，先注释
	//if err = b.checkDeviceVersion(req.DeviceID); err != nil {
	//	return
	//}

	// 机型验证
	var machineType string
	if req.Content.Model != "其他(请在问题详情中描述)" {
		machineType, err = b.validateModel(c, req.Content.Model)
		if err != nil {
			return
		}
		// 手表IMEI验证
		if machineType == "watch" {
			err = b.validateWatchIMEI(c, req.Content.IMEI)
			if err != nil {
				return
			}
		}
	}

	// 机器标识码验证
	if err = b.validateMachineIdentity(req.Content.MachineIdentity); err != nil {
		return
	}

	// 提交反馈
	err = b.bugfreeSvc.PostBugfreeAddV4(
		c,
		b.convertContentToMap(&req.Content),
	)
	if err != nil {
		return
	}

	resp = &dto.BugfreeAddV4Resp{
		Data: true,
	}
}

// PostBugfreeRepairAddV2 提交维修反馈（v2版本），重构了python的实现，TODO: 当前如果想成功获取外部api返回的结果还需要传app_id等信息
func (b *bugfree) PostBugfreeRepairAddV2(c *gin.Context) {
	var (
		req  client.BugfreeRepairAddV2Request
		err  error
		resp *dto.BugfreeRepairAddV2Resp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}

	// 设备版本检查，TODO:新版app在企微，暂时先不校验设备ID
	// if err = b.checkDeviceVersion(req.DeviceID); err != nil {
	//	 return
	// }

	// 机型验证
	machineType, err := b.validateModel(c, req.Content.Model)
	if err != nil {
		return
	}

	// 手表IMEI验证
	if machineType == "watch" {
		err = b.validateWatchIMEI(c, req.Content.IMEI)
		if err != nil {
			return
		}
	}

	// 机器标识码验证
	if err = b.validateMachineIdentity(req.Content.MachineIdentity); err != nil {
		return
	}

	// 提交维修反馈
	err = b.bugfreeSvc.PostBugfreeRepairAddV2(
		c,
		b.convertRepairContentToMap(&req.Content),
	)
	if err != nil {
		return
	}

	resp = &dto.BugfreeRepairAddV2Resp{
		Data: true,
	}
}

// convertRepairContentToMap 将维修反馈content转换为map[string]interface{}用于外部API调用
func (b *bugfree) convertRepairContentToMap(content *client.BugfreeRepairAddV2Content) map[string]interface{} {
	result := map[string]interface{}{
		"user_id":      content.UserID,
		"model":        content.Model,
		"product_id":   content.ProductID,
		"title":        content.Title,
		"description":  content.Description,
		"username":     content.Username,
		"phone_number": content.PhoneNumber,
	}

	// 添加可选字段
	if content.IMEI != "" {
		result["imei"] = content.IMEI
	}
	if content.MachineIdentity != "" {
		result["machine_identity"] = content.MachineIdentity
	}
	if content.RepeatStep != "" {
		result["repeat_step"] = content.RepeatStep
	}
	if content.SoftwareType != "" {
		result["software_type"] = content.SoftwareType
	}
	if content.HardwareType != "" {
		result["hardware_type"] = content.HardwareType
	}
	if content.SuggestionType != "" {
		result["suggestion_type"] = content.SuggestionType
	}
	if len(content.Attachments) > 0 {
		result["attachments"] = content.Attachments
	}

	return result
}

// convertContentToMap 将content转换为map[string]interface{}用于外部API调用
func (b *bugfree) convertContentToMap(content *client.BugfreeAddV4Content) map[string]interface{} {
	result := map[string]interface{}{
		"user_id":      content.UserID,
		"model":        content.Model,
		"product_id":   content.ProductID,
		"title":        content.Title,
		"description":  content.Description,
		"username":     content.Username,
		"phone_number": content.PhoneNumber,
	}

	// 添加可选字段
	if content.IMEI != "" {
		result["imei"] = content.IMEI
	}
	if content.MachineIdentity != "" {
		result["machine_identity"] = content.MachineIdentity
	}
	if content.RepeatStep != "" {
		result["repeat_step"] = content.RepeatStep
	}
	if content.ResourceType != "" {
		result["resource_type"] = content.ResourceType
	}
	if content.ResourceName != "" {
		result["resource_name"] = content.ResourceName
	}
	if content.Press != "" {
		result["press"] = content.Press
	}
	if content.Grade != "" {
		result["grade"] = content.Grade
	}
	if content.Course != "" {
		result["course"] = content.Course
	}
	if content.ResourceVolume != "" {
		result["resource_volume"] = content.ResourceVolume
	}
	if content.SoftwareType != "" {
		result["software_type"] = content.SoftwareType
	}
	if content.HardwareType != "" {
		result["hardware_type"] = content.HardwareType
	}
	if content.SuggestionType != "" {
		result["suggestion_type"] = content.SuggestionType
	}
	if len(content.Attachments) > 0 {
		result["attachments"] = content.Attachments
	}

	return result
}

// checkDeviceVersion 检查设备版本
func (b *bugfree) checkDeviceVersion(deviceID int) error {
	deviceIdStr := strconv.Itoa(deviceID)
	// 解析设备ID格式：info = device_id.split('/', 4)
	parts := strings.Split(deviceIdStr, "/")
	if len(parts) < 4 {
		return errors.New("反馈信息失败，请更新最新版本")
	}

	// 提取应用版本号
	appVersion, err := strconv.Atoi(parts[3])
	if err != nil {
		return errors.New("反馈信息失败，请更新最新版本")
	}

	// 提取应用ID
	appID := parts[2]

	// 检查iOS应用版本
	iosApps := []string{"com.readboy.care.inhouse", "com.readboy.terminal"}
	iosVersion := 20201203

	// 检查Android应用版本
	androidApps := []string{"com.readboy.readboyscan"}
	androidVersion := 420092301

	// 验证版本
	for _, iosApp := range iosApps {
		if appID == iosApp && appVersion >= iosVersion {
			return nil
		}
	}

	for _, androidApp := range androidApps {
		if appID == androidApp && appVersion >= androidVersion {
			return nil
		}
	}

	return errors.New("反馈信息失败，请更新最新版本")
}

// validateModel 验证机型
func (b *bugfree) validateModel(c *gin.Context, model string) (string, error) {
	// 调用service层验证机型是否存在
	machineType, err := b.bugfreeSvc.ValidateMachineModel(c, model)
	if err != nil {
		return "", err
	}
	return machineType, nil
}

// validateWatchIMEI 验证手表IMEI
func (b *bugfree) validateWatchIMEI(_ *gin.Context, imei string) error {
	// 验证IMEI有效性
	if imei != "" {
		params := utils.CheckMachineParams{Imei: imei}
		machine, err := utils.CheckMachine(params)
		if err != nil {
			return errors.Wrap(err, "IMEI码错误")
		}
		if machine == nil {
			return errors.New("IMEI码错误")
		}
		return nil
	}

	return errors.New("IMEI不能为空")
}

// validateMachineIdentity 验证机器标识码
func (b *bugfree) validateMachineIdentity(identity string) error {
	if identity != "" {
		// 检查是否为IMEI格式
		if utils.IsImei(identity) {
			params := utils.CheckMachineParams{Imei: identity}
			machine, err := utils.CheckMachine(params)
			if err != nil {
				return errors.Wrap(err, "条码或序列号或IMEI码错误")
			}
			if machine == nil {
				return errors.New("条码或序列号或IMEI码错误")
			}
		}
	}

	return nil
}

// PostBugfreeUploadFile 上传文件
func (b *bugfree) PostBugfreeUploadFile(c *gin.Context) {
	var (
		err  error
		resp *dto.UploadFileResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 获取上传的文件
	file, err := c.FormFile("images")
	if err != nil {
		err = errors.Wrap(err, "获取上传文件失败")
		return
	}

	// 打开文件
	src, err := file.Open()
	if err != nil {
		err = errors.Wrap(err, "打开文件失败")
		return
	}
	defer func(src multipart.File) {
		_ = src.Close()
	}(src)

	// 读取文件内容
	fileData := make([]byte, file.Size)
	_, err = src.Read(fileData)
	if err != nil {
		err = errors.Wrap(err, "读取文件内容失败")
		return
	}

	// 创建OSS服务
	ossService, err := oss.NewOSSService()
	if err != nil {
		err = errors.Wrap(err, "创建OSS服务失败")
		return
	}

	// 创建上传文件服务
	uploadService := service.NewUploadFileService(ossService)

	// 上传文件
	result, err := uploadService.UploadFile(c, fileData, file.Filename)
	if err != nil {
		err = errors.Wrap(err, "上传文件失败")
		return
	}

	// 构建响应
	resp = &dto.UploadFileResponse{
		Name: result.Name,
		Mime: result.Mime,
		Size: result.Size,
		URL:  result.URL,
	}
}
