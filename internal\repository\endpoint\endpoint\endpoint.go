package endpoint

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing-app/internal/model"
	"marketing-app/internal/repository/endpoint/endpoint/dto"
	"time"
)

type Endpoint interface {
	GetEndpointAgency(c *gin.Context, id int) (data dto.EndpointAgency, err error)
	Activate(c *gin.Context, id int) error
	CheckPermission(c *gin.Context, id int, permission string) (bool, error)
}

type endpoint struct {
	db *gorm.DB
}

func NewEndpoint(db *gorm.DB) Endpoint {
	return &endpoint{
		db: db,
	}
}

func (e *endpoint) Activate(c *gin.Context, id int) (err error) {
	return e.db.WithContext(c).Model(&model.Endpoint{}).Where("id = ?", id).Update("active_at", time.Now()).Error
}

func (e *endpoint) CheckPermission(c *gin.Context, id int, permission string) (bool, error) {
	var count int64
	err := e.db.WithContext(c).
		Table("admin_permissions as ap").
		Joins("RIGHT JOIN endpoint_permissions ep ON ap.id = ep.permission_id").
		Where("ep.endpoint_id = ? AND ap.slug = ?", id, permission).
		Count(&count).Error
	return count > 0, err
}

func (e *endpoint) GetEndpointAgency(c *gin.Context, id int) (data dto.EndpointAgency, err error) {
	err = e.db.WithContext(c).
		Table("endpoint as e").
		Select("e.id, e.top_agency, e.second_agency, e.is_direct_sales, e.compound, a.channel as endpoint_channel, e.status").
		Joins("LEFT JOIN agency a ON e.top_agency = a.id").
		Where("e.id = ?", id).
		Find(&data).Error
	return
}
