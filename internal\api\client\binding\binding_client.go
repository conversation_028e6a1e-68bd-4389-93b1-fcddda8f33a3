package binding

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"io"
	"marketing-app/internal/api/client/binding/config"
	"marketing-app/internal/pkg/log"
	"net/http"
	"net/url"
	"strconv"
	"time"
)

type BindingClient struct {
	httpClient *http.Client
	cfg        *config.AppConfig
}

func NewBindingClient(cfg *config.AppConfig) BindingClient {
	return BindingClient{
		httpClient: &http.Client{
			Timeout: cfg.HTTPTimeout,
		},
		cfg: cfg,
	}
}

func (b *BindingClient) CancelBinding(c *gin.Context, number string) (bool, error) {
	param := b.getBindingParam(number)
	requestURL := b.cfg.Host + "?" + param.Encode()
	req, err := http.NewRequestWithContext(c, http.MethodGet, requestURL, nil)
	if err != nil {
		err = errors.Wrap(err, "系统错误")
		return false, err
	}
	resp, err := b.httpClient.Do(req)
	if err != nil {
		err = errors.Wrap(err, "系统错误")
		return false, err
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		err = errors.Wrap(err, "系统错误")
		return false, err
	}

	var result map[string]interface{}
	if err = json.Unmarshal(body, &result); err != nil {
		err = errors.Wrap(err, "系统错误")
		return false, err
	}

	// 处理响应结果
	if status, ok := result["status"].(float64); ok {
		if status == 1 {
			return true, nil
		} else if status == 0 {
			if errMsg, ok := result["errmsg"].(string); ok {
				log.Error(errMsg)
				return false, nil
			}
			err = errors.New("API返回错误")
			return false, err
		}
	}
	err = errors.Wrap(err, "系统错误")
	return false, err
}

func (b *BindingClient) getBindingParam(number string) url.Values {
	// 生成时间戳
	ts := strconv.FormatInt(time.Now().Unix(), 10)
	// 生成签名
	appidMD5 := md5.Sum([]byte(b.cfg.AppID))
	appidMD5Hex := hex.EncodeToString(appidMD5[:])
	signStr := ts + b.cfg.AppSec + appidMD5Hex
	signMD5 := md5.Sum([]byte(signStr))
	signMD5Hex := hex.EncodeToString(signMD5[:])
	sn := "00000000" + ts + signMD5Hex + b.cfg.AppID
	// 构建请求参数
	params := url.Values{}
	params.Set("imei", number)
	params.Set("sn", sn)
	return params
}
