package builder

import (
	"gorm.io/gorm"
)

type ContactMachine struct {
	EndpointId int
	Name       string
}

func NewContactMachine() *ContactMachine {
	return &ContactMachine{}
}

func (c *ContactMachine) Fill(db *gorm.DB) *gorm.DB {
	db = c.fillWhere(db)
	return db
}

func (c *ContactMachine) fillWhere(db *gorm.DB) *gorm.DB {
	if c.EndpointId != 0 {
		db = db.Where("endpoint_id = ?", c.EndpointId)
	}
	if c.Name != "" {
		db = db.Where("name = ?", c.Name)
	}
	return db
}

func (c *ContactMachine) NameEq(name string) *ContactMachine {
	c.Name = name
	return c
}

func (c *ContactMachine) EndpointIdEq(v int) *ContactMachine {
	c.EndpointId = v
	return c
}
