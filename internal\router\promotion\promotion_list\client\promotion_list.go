package client

// PromotionListsRequest 促销活动列表请求
type PromotionListsRequest struct {
	Id       int    `form:"id" json:"id" binding:"required"`                    // 促销活动ID
	Agency   int    `form:"agency" json:"agency"`                               // 代理商ID
	Endpoint int    `form:"endpoint" json:"endpoint"`                           // 终端ID
	Receipt  int    `form:"receipt" json:"receipt"`                             // 回执状态 1-已上传 2-未上传
	ModelId  []int  `form:"model_id" json:"model_id"`                           // 机型ID列表
	Keyword  string `form:"keyword" json:"keyword"`                             // 关键词搜索(条码)
	Page     int    `form:"page" json:"page" binding:"required,min=1"`          // 页码
	PageSize int    `form:"page_size" json:"page_size" binding:"required,min=1"` // 每页数量
}

// PromotionListDetailRequest 促销活动详情请求
type PromotionListDetailRequest struct {
	Id int `uri:"id" binding:"required,min=1"` // 促销活动列表ID
}

// ReceiptUploadRequest 上传回执请求
type ReceiptUploadRequest struct {
	Id      int    `uri:"id" binding:"required,min=1"`      // 促销活动列表ID
	Receipt string `json:"receipt" binding:"required"`      // 回执图片，多个用逗号分隔
}
