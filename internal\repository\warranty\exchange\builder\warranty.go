package builder

import "gorm.io/gorm"

type Warranty struct {
	Barcode    string
	ExtBarcode string
	Status     []int
	DoOmit     []string
	Selects    string

	BarcodeOrExtBarcode bool   `json:"barcode_or_ext_barcode"`
	DoJoinMachineType   string `json:"join_machine_type"`
}

func NewWarranty() *Warranty {
	return &Warranty{}
}

func (w *Warranty) Fill(db *gorm.DB) *gorm.DB {
	db = w.fillWhere(db)
	if w.DoJoinMachineType != "" {
		db.Joins(w.DoJoinMachineType)
	}
	if len(w.DoOmit) > 0 {
		db = db.Omit(w.DoOmit...)
	}
	if w.Selects != "" {
		db = db.Select(w.Selects)
	}
	return db
}

func (w *Warranty) fillWhere(db *gorm.DB) *gorm.DB {
	if w.BarcodeOrExtBarcode {
		db = db.Where("barcode = ? OR ext_barcode = ?", w.Barcode, w.ExtBarcode)
	} else if w.Barcode != "" {
		db = db.Where("barcode = ?", w.Barcode)
	}
	if len(w.Status) > 0 {
		db = db.Where("status in (?)", w.Status)
	}
	return db
}

func (w *Warranty) BarcodeEq(v string) *Warranty {
	w.Barcode = v
	return w
}

func (w *Warranty) StatusIn(v ...int) *Warranty {
	w.Status = v
	return w
}

func (w *Warranty) JoinMachineType(v string) *Warranty {
	w.DoJoinMachineType = v
	return w
}

func (w *Warranty) BarcodeOrExtBarcodeEq(barcode string, extBarcode string) *Warranty {
	w.BarcodeOrExtBarcode = true
	w.Barcode = barcode
	w.ExtBarcode = extBarcode
	return w
}

func (w *Warranty) Omits(v ...string) *Warranty {
	w.DoOmit = v
	return w
}

func (w *Warranty) Select(v string) *Warranty {
	w.Selects = v
	return w
}
