package cache

import (
	"os"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

const (
	PROTOTYPE = "prototype"
)

type PrototypeCache interface {
	Set(c *gin.Context, barcode string) error
	Del(c *gin.Context, barcode string) error
}

type prototypeCache struct {
	cache *redis.Client
}

func NewPrototypeCache() PrototypeCache {
	addr := os.Getenv("CARE_REDIS_ADDR")
	password := os.Getenv("CARE_REDIS_PASSWORD")
	if addr == "" {
		addr = "47.106.174.126:6379"
	}
	if password == "" {
		password = "ILq38ppdVT96TDtj"
	}
	cache := redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: password,
		DB:       2,
	})
	return &prototypeCache{cache: cache}
}

func (p *prototypeCache) Set(c *gin.Context, number string) error {
	// 使用ZAdd添加到有序集合中，分数设为1
	return p.cache.ZAdd(c, PROTOTYPE, redis.Z{
		Score:  1,
		Member: number,
	}).Err()
}

func (p *prototypeCache) Del(c *gin.Context, number string) error {
	// 使用ZRem从有序集合中删除
	return p.cache.ZRem(c, PROTOTYPE, number).Err()
}
