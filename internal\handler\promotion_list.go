package handler

import (
	"fmt"
	"marketing-app/internal/handler/dto"
	"marketing-app/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type PromotionListHandler interface {
	GetList(c *gin.Context)
	GetDetail(c *gin.Context)
	UploadReceipt(c *gin.Context)
}

type promotionListHandler struct {
	service service.PromotionListService
}

func NewPromotionListHandler(service service.PromotionListService) PromotionListHandler {
	return &promotionListHandler{
		service: service,
	}
}

// GetList 获取促销活动列表
func (h *promotionListHandler) GetList(c *gin.Context) {
	var req dto.PromotionListsReq
	var err error

	// 绑定请求参数
	if err = c.ShouldBind(&req); err != nil {
		ResponseError(c, err)
		return
	}

	// 获取用户信息
	var isEndpoint bool
	var endpoint int

	// 检查是否是终端用户 - 根据实际认证中间件调整
	if endpointVal, exists := c.Get("endpoint"); exists {
		if endpointInfo, ok := endpointVal.(map[string]interface{}); ok {
			if id, ok := endpointInfo["id"].(int); ok {
				endpoint = id
				isEndpoint = true
				req.Endpoint = endpoint // 终端用户只能查看自己的数据
			}
		}
	}

	// 调用服务
	data, err := h.service.GetList(c, &req, isEndpoint)
	if err != nil {
		ResponseError(c, err)
		return
	}

	ResponseSuccess(c, data)
}

// GetDetail 获取促销活动详情
func (h *promotionListHandler) GetDetail(c *gin.Context) {
	var req dto.PromotionListDetailReq
	if err := c.ShouldBindUri(&req); err != nil {
		ResponseError(c, err)
		return
	}

	// 检查是否是终端用户
	var isEndpoint bool
	if _, exists := c.Get("endpoint"); exists {
		isEndpoint = true
	}

	// 调用服务
	data, err := h.service.GetDetail(c, &req, isEndpoint)
	if err != nil {
		ResponseError(c, err)
		return
	}

	ResponseSuccess(c, data)
}

// UploadReceipt 上传回执
func (h *promotionListHandler) UploadReceipt(c *gin.Context) {
	var req dto.ReceiptUploadReq
	var err error

	// 绑定URI参数
	if err = c.ShouldBindUri(&req); err != nil {
		ResponseError(c, err)
		return
	}

	// 绑定JSON参数
	var jsonReq struct {
		Receipt string `json:"receipt" binding:"required"`
	}
	if err = c.ShouldBindJSON(&jsonReq); err != nil {
		ResponseError(c, err)
		return
	}
	req.Receipt = jsonReq.Receipt

	// 获取终端信息 - 根据实际认证中间件调整
	var endpoint int
	if endpointVal, exists := c.Get("endpoint"); exists {
		if endpointInfo, ok := endpointVal.(map[string]interface{}); ok {
			if id, ok := endpointInfo["id"].(int); ok {
				endpoint = id
			} else {
				ResponseError(c, fmt.Errorf("unauthorized"))
				return
			}
		} else {
			ResponseError(c, fmt.Errorf("unauthorized"))
			return
		}
	} else {
		// Debug模式下可以通过参数传递
		if gin.Mode() == gin.DebugMode {
			endpoint = cast.ToInt(c.Query("endpoint"))
			if endpoint == 0 {
				ResponseError(c, fmt.Errorf("endpoint required in debug mode"))
				return
			}
		} else {
			ResponseError(c, fmt.Errorf("unauthorized"))
			return
		}
	}

	// 调用服务
	err = h.service.UploadReceipt(c, &req, endpoint)
	if err != nil {
		ResponseError(c, err)
		return
	}

	ResponseSuccess(c, dto.ReceiptUploadResp{
		Message: "上传成功",
	})
}
