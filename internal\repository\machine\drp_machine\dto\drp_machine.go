package dto

import "time"

type Warranty struct {
	Id              int
	Type            int
	BuyDate         *time.Time
	Endpoint        int
	CustomerPhone   string
	Barcode         string
	ExtBarcode      string
	Status          int
	State           int
	ActivatedAtOld  *time.Time
	Number          string
	Imei            string
	Model           string
	ModelId         int
	CustomerPrice   float64
	ProductDate     *time.Time
	CreatedAt       *time.Time
	Salesman        string
	SalesmanId      int
	CustomerName    string
	CustomerSex     string
	CustomerAddr    string
	WarrantyPeriod  *time.Time
	Lng             string
	Lat             string
	StudentUID      int
	StudentName     string
	StudentSex      string
	StudentSchool   string
	StudentGrade    string
	StudentBirthday *time.Time
	UpdatedAt       *time.Time
	PurchaseWay     string
	Assessment      int
	RealSale        *int
}
