package model

import (
	"database/sql"
	"time"
)

// AppNotification 应用通知表
type AppNotification struct {
	ID        uint         `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	TypeID    uint         `gorm:"column:type_id;not null;comment:通知类型ID" json:"type_id"`
	Platform  string       `gorm:"column:platform;type:varchar(20);default:null;comment:推送平台，android / ios / all" json:"platform"`
	Content   string       `gorm:"column:content;type:text;not null;comment:消息内容" json:"content"`
	Audience  string       `gorm:"column:audience;type:text;not null;comment:推送目标，参考https://docs.jiguang.cn/jpush/server/push/rest_api_v3_push/#audience" json:"audience"`
	Revoked   uint8        `gorm:"column:revoked;type:tinyint(3) unsigned;not null;default:0;comment:是否已撤销" json:"revoked"`
	RevokedAt sql.NullTime `gorm:"column:revoked_at;default:null;comment:撤销时间" json:"revoked_at"`
	CreatedAt time.Time    `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt time.Time    `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// TableName 设置表名
func (AppNotification) TableName() string {
	return "app_notification"
}
