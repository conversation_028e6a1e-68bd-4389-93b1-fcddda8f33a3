package model

import (
	"time"
)

// BugfreeNotice 用户反馈——反馈须知
type BugfreeNotice struct {
	Id        int       `json:"id" gorm:"column:id;primaryKey;autoIncrement"`
	Data      string    `json:"data" gorm:"column:data;type:varchar(4096);not null;comment:反馈须知文本内容"`
	Status    int       `json:"status" gorm:"column:status;type:tinyint(2);not null;default:1;comment:0：禁用；1：启用"`
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
}

// TableName 指定表名
func (BugfreeNotice) TableName() string {
	return "bugfree_notice"
}
