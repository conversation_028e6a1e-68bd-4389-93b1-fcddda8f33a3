package config

import (
	"marketing-app/internal/pkg/config"
	"time"
)

type AppConfig struct {
	Host        string
	AppID       string
	AppSecret   string
	HTTPTimeout time.Duration
}

func LoadBugfreeConfig() *AppConfig {
	return &AppConfig{
		Host:        config.GetString("bugfree.host"),
		AppID:       config.GetString("bugfree.app_id"),
		AppSecret:   config.GetString("bugfree.app_secret"),
		HTTPTimeout: 10 * time.Second,
	}
}
