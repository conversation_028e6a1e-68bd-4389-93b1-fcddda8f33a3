package client

type WarrantyRequest struct {
	Uid          int    `json:"uid,omitempty"`
	SalesmanId   int    `json:"salesman_id,omitempty"`
	BuyDateStart string `json:"buy_date_start,omitempty"`
	BuyDateEnd   string `json:"buy_date_end,omitempty"`
	Model        string `json:"model,omitempty"`
	Page         int    `json:"page,omitempty"`
	PageSize     int    `json:"page_size,omitempty"`
}

type IdentifierQueryRequest struct {
	Uid        int    `json:"uid,omitempty"`
	Identifier string `json:"identifier,omitempty"`
	Page       int    `json:"page,omitempty"`
	PageSize   int    `json:"page_size,omitempty"`
}
