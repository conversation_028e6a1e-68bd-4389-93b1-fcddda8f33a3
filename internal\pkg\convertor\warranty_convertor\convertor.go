package warranty_convertor

import (
	"marketing-app/internal/consts"
	"marketing-app/internal/pkg/utils"
	clientEntry "marketing-app/internal/router/warranty/warranty_entry/client"
	clientExchange "marketing-app/internal/router/warranty/warranty_exchange/client"
	clientBase "marketing-app/internal/router/warranty/warranty_homepage/client"
	clientReturn "marketing-app/internal/router/warranty/warranty_return/client"
	entityEntry "marketing-app/internal/service/warranty/warranty_entry/entity"
	entityExchange "marketing-app/internal/service/warranty/warranty_exchange/entity"
	entityBase "marketing-app/internal/service/warranty/warranty_homepage/entity"
	entityReturn "marketing-app/internal/service/warranty/warranty_return/entity"
	"time"
)

type WarrantyBaseConvertor struct{}
type IdentifierConvertor struct {
}
type WarrantyEntryConvertor struct{}
type WarrantyReturnConvertor struct{}
type WarrantyExchangeConvertor struct{}

func (c *WarrantyBaseConvertor) ClientToEntity(in *clientBase.WarrantyRequest) *entityBase.Warranty {
	s := utils.DateFormat(time.DateOnly, in.BuyDateStart)
	e := utils.DateFormat(time.DateOnly, in.BuyDateEnd)
	return &entityBase.Warranty{
		Uid:          in.Uid,
		SalesmanId:   in.SalesmanId,
		BuyDateStart: s,
		BuyDateEnd:   e,
		Model:        in.Model,
		Page:         in.Page,
		PageSize:     in.PageSize,
	}
}

func (c *IdentifierConvertor) ClientToEntity(in *clientBase.IdentifierQueryRequest) *entityBase.Identifier {
	return &entityBase.Identifier{
		Uid:      in.Uid,
		Query:    in.Identifier,
		Page:     in.Page,
		PageSize: in.PageSize,
	}
}

func (c *WarrantyEntryConvertor) ClientToEntity(in *clientEntry.CreateWarrantyRequest) *entityEntry.Warranty {
	now := time.Now()
	buyDate := utils.DateFormat(time.DateOnly, in.BuyDate)
	studentBirthday := utils.DateFormat(time.DateOnly, in.StudentBirthday)
	return &entityEntry.Warranty{
		Uid:              in.Uid,
		Barcode:          in.Barcode,
		BuyDate:          buyDate,
		CustomerPhone:    in.CustomerPhone,
		CustomerName:     in.CustomerName,
		CustomerSex:      in.CustomerSex,
		PurchaseWay:      in.PurchaseWay,
		StudentName:      in.StudentName,
		StudentSchool:    in.StudentSchool,
		StudentGrade:     in.StudentGrade,
		StudentBirthday:  studentBirthday,
		StudentSex:       in.StudentSex,
		Salesman:         in.Salesman,
		SalesmanID:       in.Uid,
		CreatedAt:        &now,
		Recommender:      in.Recommender,
		RecommenderPhone: in.RecommenderPhone,

		Type:    consts.WarrantyTypeNormal,    // 类型：1正常，-1-已冻结
		Contact: consts.WarrantyDoLoadContact, // 同步联系人信息
	}
}

func (c *WarrantyReturnConvertor) ClientToEntity(in *clientReturn.ReturnWarrantyRequest) *entityReturn.Warranty {
	returnDate := utils.DateFormat(time.DateOnly, in.ReturnDate)
	return &entityReturn.Warranty{
		Uid:        in.Uid,
		Barcode:    in.Barcode,
		ReturnDate: returnDate,
		Reason:     in.Reason,
	}
}

func (c *WarrantyExchangeConvertor) ClientToEntity(in *clientExchange.ExchangeWarrantyRequest) *entityExchange.Warranty {
	// exchangeDate := utils.DateFormat(time.DateOnly, in.ExchangeDate)
	// 在py中，无论前端传来什么时间都是把换货时间变成当前时间
	now := time.Now()
	return &entityExchange.Warranty{
		Uid:          in.Uid,
		Barcode:      in.Barcode,
		NewBarcode:   in.NewBarcode,
		Reason:       in.Reason,
		ExchangeDate: &now,
	}
}
