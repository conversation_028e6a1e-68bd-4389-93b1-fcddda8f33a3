package cache

import (
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"marketing-app/internal/model"
	"time"
)

const (
	AppSystemCacheKey = "app_system_cache_key:"
)

type AppSystemCache interface {
	GetAppSystemByKey(c *gin.Context, key string) (*model.AppSystemV2, error)
	SetAppSystem(c *gin.Context, appSystem *model.AppSystemV2, key string, expiration time.Duration) error
	DeleteAppSystem(c *gin.Context, key string) error
}

type appSystemCache struct {
	client *redis.Client
}

func NewAppSystemCache(client *redis.Client) AppSystemCache {
	return &appSystemCache{
		client: client,
	}
}

func (s *appSystemCache) GetAppSystemByKey(c *gin.Context, key string) (*model.AppSystemV2, error) {
	if key == "" {
		return nil, fmt.Errorf("key is empty")
	}
	key = AppSystemCacheKey + key
	data, err := s.client.Get(c, key).Bytes()
	if err != nil {
		return nil, err
	}
	var appSystem model.AppSystemV2
	if err := json.Unmarshal(data, &appSystem); err != nil {
		return nil, err
	}
	return &appSystem, nil
}

func (s *appSystemCache) SetAppSystem(c *gin.Context, appSystem *model.AppSystemV2, key string, expiration time.Duration) error {
	key = AppSystemCacheKey + key
	data, err := json.Marshal(appSystem)
	if err != nil {
		return err
	}
	return s.client.Set(c, key, data, expiration).Err()
}

func (s *appSystemCache) DeleteAppSystem(c *gin.Context, key string) error {
	if key == "" {
		return fmt.Errorf("key is empty")
	}
	key = AppSystemCacheKey + key
	return s.client.Del(c, key).Err()
}
