package model

import (
	"time"
)

// Subject 学科表
type Subject struct {
	Id         int       `json:"id" gorm:"column:id;primaryKey;autoIncrement"`                                                                                 // 主键ID
	Name       string    `json:"name" gorm:"column:name;type:varchar(100);comment:科目名称"`                                                                       // 科目名称
	Visibility int       `json:"visibility" gorm:"column:visibility;type:tinyint;default:1;not null;comment:可见性,由于是直接读取生产的数据库,所以里面有些机型不能先让人看到,需要隐藏,0是隐藏,1是可见"` // 可见性
	CreatedAt  time.Time `json:"created_at" gorm:"column:created_at;type:timestamp;default:NULL"`                                                              // 创建时间
	UpdatedAt  time.Time `json:"updated_at" gorm:"column:updated_at;type:timestamp;default:NULL ON UPDATE CURRENT_TIMESTAMP"`                                  // 更新时间
}

// TableName 指定表名
func (Subject) TableName() string {
	return "subject"
}
