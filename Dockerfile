FROM registry.cn-shenzhen.aliyuncs.com/readboy_zs/golang:1.24.5-alpine3.22 AS builder

# 设置 Go 模块代理
ENV GOPROXY=https://goproxy.io,direct

WORKDIR /app

# 复制 go.mod 和 go.sum 文件以便下载依赖
COPY go.mod go.sum ./

RUN go mod download && go mod verify

COPY . .

RUN CGO_ENABLED=0 GOOS=linux go build -o marketing-app .

# 检查构建结果
RUN ls -al /app

# 使用更小的基础镜像来运行应用
FROM registry-vpc.cn-shenzhen.aliyuncs.com/readboy_zs/alpine:latest

# 从构建阶段复制可执行文件
COPY --from=builder /app/marketing-app .

# 检查构建结果
RUN ls -al .

# 暴露端口（根据需要）
EXPOSE 8088

# 指定容器启动时要执行的命令
CMD ["./marketing-app"]