package wecom

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"marketing-app/internal/pkg/log"
)

// UserInfoResp UserInfoResp用于解析获取用户信息的响应
type UserInfoResp struct {
	ErrCode    int    `json:"errcode"`
	ErrMsg     string `json:"errmsg"`
	UserID     string `json:"userid"`
	Name       string `json:"name"`
	UserTicket string `json:"user_ticket"` // 用户票据
}

// GetUserIDByCode 通过 code 获取 UserID
func (client *Client) GetUserIDByCode(code string) (string, error) {
	accessToken, err := client.GetAccessToken()
	if err != nil {
		return "", err
	}
	url := fmt.Sprintf("https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token=%s&code=%s", accessToken, code)

	resp, err := client.httpClient.R().Get(url)
	if err != nil {
		return "", err
	}
	//正式不应该输出
	log.Info("企微code登录：", zap.Any("企微code响应", resp))

	var userInfo UserInfoResp
	if err := json.Unmarshal(resp.Body(), &userInfo); err != nil {
		log.Error("微信code请求用户信息错误,json解析错误：", zap.Error(err))
		return "", err
	}

	if userInfo.ErrCode != 0 {
		log.Error("微信code请求用户信息错误，微信接口错误：", zap.Int("errcode", userInfo.ErrCode), zap.String("errmsg", userInfo.ErrMsg), zap.String("URL", url))
		return "", &WeChatAPIError{ErrCode: userInfo.ErrCode, ErrMsg: userInfo.ErrMsg}
	}

	if userInfo.UserID == "" {
		return "", fmt.Errorf("无权访问，可能是非企业成员或用户未授权")
	}
	return userInfo.UserID, nil
}
