package prototype_convertor

import (
	"marketing-app/internal/router/prototype/prototype_manage/client"
	"marketing-app/internal/service/prototype/prototype_manage/entity"
)

type PrototypeCreateConvertor struct{}
type PrototypeListConvertor struct{}
type PrototypeDeleteConvertor struct{}
type PrototypeCheckConvertor struct{}

func (c *PrototypeCreateConvertor) ClientToEntity(in *client.CreatePrototypeRequest) *entity.Prototype {
	return &entity.Prototype{
		Uid:        in.Uid,
		Barcode:    in.Barcode,
		EndpointId: in.EndpointId,
	}
}

func (c *PrototypeListConvertor) ClientToEntity(in *client.ListPrototypeRequest) *entity.ListPrototype {
	if in.Page <= 0 {
		in.Page = 1
	}
	if in.PageSize <= 0 {
		in.PageSize = 10
	}
	return &entity.ListPrototype{
		EndpointId: in.EndpointId,
		Page:       in.Page,
		PageSize:   in.PageSize,
	}
}

func (c *PrototypeDeleteConvertor) ClientToEntity(in *client.DeletePrototypeRequest) *entity.DeletePrototype {
	return &entity.DeletePrototype{
		Barcode:    in.Barcode,
		EndpointId: in.EndpointID,
	}
}

func (c *PrototypeCheckConvertor) ClientToEntity(in *client.CheckPrototypeRequest) *entity.CheckPrototype {
	return &entity.CheckPrototype{
		Barcode: in.Barcode,
	}
}
