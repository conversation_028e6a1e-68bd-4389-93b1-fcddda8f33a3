package service

import (
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"marketing-app/internal/cache"
	"marketing-app/internal/model"
	"marketing-app/internal/pkg/log"
	"marketing-app/internal/repository"
)

type AppSystemService interface {
	// GetAppSystem 获取应用系统配置
	GetAppSystem(c *gin.Context) (*model.AppSystemV2, error)
}

type appSystemService struct {
	appSystemCache cache.AppSystemCache
	appSystemRepo  repository.AppSystemRepository
}

func NewAppSystemService(appSystemCache cache.AppSystemCache,
	appSystemRepo repository.AppSystemRepository) AppSystemService {
	return &appSystemService{
		appSystemRepo:  appSystemRepo,
		appSystemCache: appSystemCache,
	}
}

func (s *appSystemService) GetAppSystem(c *gin.Context) (*model.AppSystemV2, error) {
	systemType := c.GetHeader("X-Gate-Type")
	if systemType == "" {
		return nil, errors.New("系统类型不能为空")
	}
	// 尝试从缓存获取系统信息
	systemInfo, err := s.appSystemCache.GetAppSystemByKey(c, systemType)
	if err != nil {
		// 处理缓存查找错误
		log.Error(fmt.Sprintf("缓存查找失败: %v", err))
	}

	if systemInfo == nil {
		// 如果缓存中没有，从数据库或其他持久化存储中查找
		systemInfo, err = s.appSystemRepo.GetAppSystemByKey(c, systemType)
		if err != nil {
			return nil, fmt.Errorf("从数据库查找失败: %w", err)
		}

		if systemInfo == nil {
			return nil, errors.New("系统不存在")
		}

		// 将获取到的系统信息缓存
		err = s.appSystemCache.SetAppSystem(c, systemInfo, systemType, 0)
		if err != nil {
			log.Error(fmt.Sprintf("缓存系统信息失败: %v", err))
		}
	}

	return systemInfo, nil
}
