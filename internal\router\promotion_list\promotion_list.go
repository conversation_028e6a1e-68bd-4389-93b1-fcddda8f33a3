package promotion_list

import (
	handler "marketing-app/internal/handler"
	"marketing-app/internal/pkg/db"
	"marketing-app/internal/repository"
	"marketing-app/internal/service"

	"github.com/gin-gonic/gin"
)

type PromotionListRouter struct {
	promotionListHandler handler.PromotionListHandler
}

func NewPromotionListRouter() *PromotionListRouter {
	database, _ := db.GetDB()
	repo := repository.NewPromotionListRepository(database)
	svc := service.NewPromotionListService(repo)
	h := handler.NewPromotionListHandler(svc)
	return &PromotionListRouter{
		promotionListHandler: h,
	}
}

func (p *PromotionListRouter) Register(r *gin.RouterGroup) {
	g := r.Group("/app/promotion")
	{
		// 名单列表
		g.GET("/list", p.promotionListHandler.GetList)
		// 名单详情
		g.GET("/list/:id", p.promotionListHandler.GetDetail)
		// 上传回执
		g.POST("/list/:id", p.promotionListHandler.UploadReceipt)
	}
}
