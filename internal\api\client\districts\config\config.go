package config

import (
	"marketing-app/internal/pkg/config"
	"time"
)

type AppConfig struct {
	Host        string
	AppSecret   string
	DeviceID    string
	HTTPTimeout time.Duration
}

func LoadDistrictsConfig() *AppConfig {
	return &AppConfig{
		Host:        config.GetString("districts.host"),
		AppSecret:   config.GetString("districts.app_secret"),
		DeviceID:    config.GetString("districts.device_id"),
		HTTPTimeout: 10 * time.Second,
	}
}
