package config

import (
	"fmt"
	"os"
	"strings"

	"github.com/spf13/viper"
)

var globalViper *viper.Viper

// Init 初始化配置
func Init(configFile string) error {
	globalViper = viper.New()

	// 设置配置文件路径
	if configFile != "" {
		globalViper.SetConfigFile(configFile)
	} else {
		// 尝试从默认路径查找
		globalViper.AddConfigPath("./conf")
		globalViper.SetConfigName("config")
	}

	// 支持的配置格式
	globalViper.SetConfigType("yaml")

	// 读取环境变量，自动转换为小写并替换_为.
	globalViper.AutomaticEnv()
	globalViper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// 读取配置文件
	if err := globalViper.ReadInConfig(); err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	return nil
}

// GetViper 获取全局Viper实例
func GetViper() *viper.Viper {
	if globalViper == nil {
		panic("配置未初始化")
	}
	return globalViper
}

// Get 获取配置值
func Get(key string) interface{} {
	return GetViper().Get(key)
}

// GetString 获取字符串配置
func GetString(key string) string {
	return GetViper().GetString(key)
}

// UnmarshalKey 将指定key的配置解析到结构体
func UnmarshalKey(key string, out interface{}) error {
	return GetViper().UnmarshalKey(key, out)
}

// GetEnv 获取环境变量，支持默认值
func GetEnv(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}
