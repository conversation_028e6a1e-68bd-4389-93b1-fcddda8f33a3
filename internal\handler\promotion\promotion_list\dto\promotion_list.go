package dto

import "time"

// PromotionListsResp 促销活动列表响应
type PromotionListsResp struct {
	List     []PromotionListItem `json:"list"`
	Total    int64               `json:"total"`
	Page     int                 `json:"page"`
	PageSize int                 `json:"page_size"`
}

// PromotionListItem 促销活动列表项
type PromotionListItem struct {
	Id                  int                           `json:"id"`
	SalesPromotionId    int                           `json:"sales_promotion_id"`
	Endpoint            int                           `json:"endpoint"`
	ModelId             int                           `json:"model_id"`
	Barcode             string                        `json:"barcode"`
	BuyDate             *time.Time                    `json:"buy_date"`
	WarrantyId          int                           `json:"warranty_id"`
	IsReceipt           int                           `json:"is_receipt"`
	ReceiptAt           *time.Time                    `json:"receipt_at"`
	RegionName          string                        `json:"region_name"`
	AgencyName          string                        `json:"agency_name"`
	EndpointName        string                        `json:"endpoint_name"`
	EndpointCode        string                        `json:"endpoint_code"`
	Manager             string                        `json:"manager"`
	EndpointPhone       string                        `json:"endpoint_phone"`
	Address             string                        `json:"address"`
	Receipt             interface{}                   `json:"receipt"` // 可能是string或[]PromotionListReceiptResp
	Number              string                        `json:"number,omitempty"`
	ActivatedAt         string                        `json:"activated_at,omitempty"`
	StudentName         string                        `json:"student_name,omitempty"`
}

// PromotionListDetailResp 促销活动详情响应
type PromotionListDetailResp struct {
	Id                  int         `json:"id"`
	SalesPromotionId    int         `json:"sales_promotion_id"`
	Endpoint            int         `json:"endpoint"`
	ModelId             int         `json:"model_id"`
	Barcode             string      `json:"barcode"`
	BuyDate             *time.Time  `json:"buy_date"`
	WarrantyId          int         `json:"warranty_id"`
	IsReceipt           int         `json:"is_receipt"`
	ReceiptAt           *time.Time  `json:"receipt_at"`
	StudentUid          string      `json:"student_uid"`
	StudentName         string      `json:"student_name"`
	AgencyName          string      `json:"agency_name"`
	ActivatedAt         string      `json:"activated_at"`
	Number              string      `json:"number"`
	HourInterval        int         `json:"hour_interval"`
	EndpointCode        string      `json:"endpoint_code"`
	EndpointName        string      `json:"endpoint_name"`
	Manager             string      `json:"manager"`
	ReturnAt            *time.Time  `json:"return_at"`
	Receipt             interface{} `json:"receipt"` // 可能是string或[]PromotionListReceiptResp
}

// PromotionListReceiptResp 回执响应
type PromotionListReceiptResp struct {
	SalesPromotionListId int    `json:"sales_promotion_list_id"`
	Receipt              string `json:"receipt"`
	Number               string `json:"number"`
	Count                int    `json:"count"`
}

// ReceiptUploadResp 上传回执响应
type ReceiptUploadResp struct {
	Message string `json:"message"`
}
