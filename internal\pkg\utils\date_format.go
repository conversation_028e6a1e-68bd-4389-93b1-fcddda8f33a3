package utils

import "time"

func DateFormatValidate(format string, dateStr ...string) error {
	a := make([]string, len(dateStr))
	a = append(a, dateStr...)

	for _, s := range a {
		if s != "" {
			if _, err := time.Parse(format, s); err != nil {
				return err
			}
		}
	}
	return nil
}

func DateFormat(format string, dateStr string) *time.Time {
	var t time.Time
	if dateStr != "" {
		t, _ = time.Parse(format, dateStr)
	}
	return &t
}
