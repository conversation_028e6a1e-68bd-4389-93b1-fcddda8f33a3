package main

import (
	"context"
	"errors"
	"fmt"
	"go.uber.org/zap"
	"marketing-app/internal/pkg/alilog"
	"marketing-app/internal/pkg/config"
	"marketing-app/internal/pkg/db"
	"marketing-app/internal/pkg/log"
	"marketing-app/internal/pkg/redis"
	"marketing-app/internal/pkg/validator"
	"marketing-app/internal/router"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"
)

func main() {
	// 初始化配置
	if err := config.Init(""); err != nil {
		panic(err)
	}

	// 初始化日志
	var logConfig log.Config
	if err := config.UnmarshalKey("Logger", &logConfig); err != nil {
		panic(fmt.Sprintf("解析数据库配置失败: %v", err))
	}
	if err := log.Init(logConfig); err != nil {
		panic(err)
	}
	defer func() {
		err := log.Sync()
		if err != nil && config.GetString("gin.mode") != "release" {
			fmt.Printf("日志同步失败: %v\n", err)
		} else {
			fmt.Println("日志已同步")
		}
	}()

	// 初始化Redis
	if err := redis.Init(); err != nil {
		panic(fmt.Sprintf("初始化Redis失败: %v", err))
	}
	defer func() {
		if err := redis.Close(); err != nil {
			log.Error("关闭Redis连接失败", zap.Error(err))
		} else {
			log.Info("Redis连接已关闭")
		}
	}()

	// 初始化数据库
	err := db.Init()
	if err != nil {
		panic(fmt.Sprintf("初始化数据库失败: %v", err))
	}
	defer func() {
		err = db.CloseAll()
		if err != nil {
			log.Error("关闭数据库连接失败", zap.Error(err))
		} else {
			log.Info("数据库连接已关闭")
		}
	}()

	validator.Init()
	//sls日志
	alilog.Init()

	// 启动 HTTP 服务器
	r := router.InitRouter()
	server := &http.Server{
		Addr:    ":" + config.GetString("server.port"),
		Handler: r,
	}

	go func() {
		log.Info(fmt.Sprintf("Server listen port: %s", config.GetString("server.port")))
		if err := server.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.Fatal("Server startup failed: %v", zap.Error(err))
		}
	}()

	// 处理关闭信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Info("Server shutting down...")
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Fatal("Could not gracefully shutdown the server: ", zap.Error(err))
	}
	log.Info("Server stopped")
}
