package builder

import "gorm.io/gorm"

type Prototype struct {
	Barcode               string
	Number                string
	Status                []int
	DoJoinMachineType     string
	DoJoinPrototypeConfig string
}

func NewPrototype() *Prototype {
	return &Prototype{}
}

func (p *Prototype) Fill(db *gorm.DB) *gorm.DB {
	db = p.fillWhere(db)
	if p.DoJoinMachineType != "" {
		db.<PERSON><PERSON>(p.DoJoinMachineType)
	}
	if p.DoJoinPrototypeConfig != "" {
		db.<PERSON><PERSON>(p.DoJoinPrototypeConfig)
	}
	return db
}

func (p *Prototype) fillWhere(db *gorm.DB) *gorm.DB {
	if p.Barcode != "" {
		db = db.Where("barcode = ?", p.Barcode)
	}
	if p.Number != "" {
		db = db.Where("number = ?", p.Number)
	}
	if len(p.Status) > 0 {
		db = db.Where("status IN (?)", p.Status)
	}
	return db
}

func (p *Prototype) BarcodeEq(v string) *Prototype {
	p.Barcode = v
	return p
}

func (p *Prototype) NumberEq(v string) *Prototype {
	p.Number = v
	return p
}

func (p *Prototype) StatusIn(v ...int) *Prototype {
	p.Status = v
	return p
}

func (p *Prototype) JoinMachineTypeEq(v string) *Prototype {
	p.DoJoinMachineType = v
	return p
}

func (p *Prototype) JoinPrototypeConfigEq(v string) *Prototype {
	p.DoJoinPrototypeConfig = v
	return p
}
