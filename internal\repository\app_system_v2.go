package repository

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing-app/internal/model"
)

type AppSystemRepository interface {
	GetAppSystemByKey(c *gin.Context, key string) (*model.AppSystemV2, error)
}

type appSystemRepo struct {
	db *gorm.DB // 假设使用 GORM 或其他 ORM 库
}

func NewAppSystemRepository(db *gorm.DB) AppSystemRepository {
	return &appSystemRepo{db: db}
}
func (r *appSystemRepo) GetAppSystemByKey(c *gin.Context, key string) (*model.AppSystemV2, error) {
	var appSystem model.AppSystemV2
	if err := r.db.WithContext(c).Where("key = ?", key).First(&appSystem).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 应用不存在
		}
		return nil, err // 其他错误
	}
	return &appSystem, nil // 返回找到的系统信息
}
