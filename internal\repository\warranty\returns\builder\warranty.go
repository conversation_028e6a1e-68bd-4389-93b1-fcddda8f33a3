package builder

import (
	"gorm.io/gorm"
)

type Warranty struct {
	Id            int
	Barcode       string
	CustomerPhone string
	Status        []int

	DoOmits []string
}

func NewWarranty() *Warranty {
	return &Warranty{}
}

func (w *Warranty) Fill(db *gorm.DB) *gorm.DB {
	db = w.fillWhere(db)
	if len(w.<PERSON>) > 0 {
		db = db.Omit(w.DoOmits...)
	}
	return db
}

func (w *Warranty) fillWhere(db *gorm.DB) *gorm.DB {
	if w.Id != 0 {
		db = db.Where("id = ?", w.Id)
	}
	if w.Barcode != "" {
		db = db.Where("barcode = ?", w.Barcode)
	}
	if len(w.Status) > 0 {
		db = db.Where("status in (?)", w.Status)
	}
	if w.CustomerPhone != "" {
		db = db.Where("customer_phone = ?", w.CustomerPhone)
	}
	return db
}

func (w *Warranty) BarcodeEq(v string) *Warranty {
	w.Barcode = v
	return w
}

func (w *Warranty) StatusIn(v ...int) *Warranty {
	w.Status = v
	return w
}

func (w *Warranty) IdEq(v int) *Warranty {
	w.Id = v
	return w
}

func (w *Warranty) CustomerPhoneEq(v string) *Warranty {
	w.CustomerPhone = v
	return w
}

func (w *Warranty) Omits(v ...string) *Warranty {
	w.DoOmits = v
	return w
}
