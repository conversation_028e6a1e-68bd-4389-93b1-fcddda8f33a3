package model

import (
	"gorm.io/gorm"
	"time"
)

type DrpRecordsDetail struct {
	OperateID int            `gorm:"column:operate_id;primaryKey;comment:操作记录id"`
	MID       int            `gorm:"column:m_id;not null;comment:机器记录id"`
	ModelID   int            `gorm:"column:model_id;comment:机型id"`
	Model     string         `gorm:"column:model;size:32;comment:机型"`
	Barcode   string         `gorm:"column:barcode;size:32;not null;primaryKey;comment:条码"` // Added primaryKey
	Remark    string         `gorm:"column:remark;size:255;comment:备注"`
	Status    int            `gorm:"column:status;default:1;comment:1--在库  2--离库  3--销售（保卡）  4--自拆样机 5--换机样机  6--退机样机"`
	NewStatus int            `gorm:"column:new_status;default:-1;comment:1--在库  2--离库  3--销售（保卡）  4--自拆样机 5--换机样机  6--退机样机"`
	Type      int            `gorm:"column:type;default:0;comment:0-正常   1-盘盈  2-盘亏"`
	BillDid   *int           `gorm:"column:bill_did;unique;comment:金蝶销售出库机器ID"`
	CreatedAt *time.Time     `gorm:"column:created_at;comment:创建时间"`
	UpdatedAt *time.Time     `gorm:"column:updated_at;comment:更新时间"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间"`
}

// TableName sets the insert table name for struct
func (DrpRecordsDetail) TableName() string {
	return "drp_records_detail"
}
