package model

import (
	"time"
)

// SalesPromotionList 促销活动列表
type SalesPromotionList struct {
	Id               int        `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	SalesPromotionId int        `gorm:"column:sales_promotion_id;not null" json:"sales_promotion_id"`
	Endpoint         int        `gorm:"column:endpoint;not null" json:"endpoint"`
	ModelId          int        `gorm:"column:model_id;not null" json:"model_id"`
	Barcode          string     `gorm:"column:barcode;type:varchar(255);not null" json:"barcode"`
	BuyDate          *time.Time `gorm:"column:buy_date" json:"buy_date"`
	WarrantyId       int        `gorm:"column:warranty_id;not null" json:"warranty_id"`
	IsReceipt        int        `gorm:"column:is_receipt;default:0" json:"is_receipt"`
	ReceiptAt        *time.Time `gorm:"column:receipt_at" json:"receipt_at"`
	CreatedAt        time.Time  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt        time.Time  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
	DeletedAt        *time.Time `gorm:"column:deleted_at" json:"deleted_at"`
}

func (SalesPromotionList) TableName() string {
	return "sales_promotion_list"
}

// SalesPromotionListReceipt 促销活动列表回执
type SalesPromotionListReceipt struct {
	Id                   int        `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	SalesPromotionListId int        `gorm:"column:sales_promotion_list_id;not null" json:"sales_promotion_list_id"`
	Receipt              string     `gorm:"column:receipt;type:varchar(500);not null" json:"receipt"`
	Number               string     `gorm:"column:number;type:varchar(100)" json:"number"`
	Type                 int        `gorm:"column:type;default:1" json:"type"`
	CreatedAt            time.Time  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt            time.Time  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
	DeletedAt            *time.Time `gorm:"column:deleted_at" json:"deleted_at"`
}

func (SalesPromotionListReceipt) TableName() string {
	return "sales_promotion_list_receipt"
}

// SalesPromotion 促销活动
type SalesPromotion struct {
	Id         int        `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Name       string     `gorm:"column:name;type:varchar(255);not null" json:"name"`
	ReceiptDay *time.Time `gorm:"column:receipt_day" json:"receipt_day"`
	CreatedAt  time.Time  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt  time.Time  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
	DeletedAt  *time.Time `gorm:"column:deleted_at" json:"deleted_at"`
}

func (SalesPromotion) TableName() string {
	return "sales_promotion"
}

// Agency 代理商
type Agency struct {
	Id        int        `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Name      string     `gorm:"column:name;type:varchar(255);not null" json:"name"`
	CreatedAt time.Time  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt time.Time  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
	DeletedAt *time.Time `gorm:"column:deleted_at" json:"deleted_at"`
}

func (Agency) TableName() string {
	return "agency"
}

// Region 地区
type Region struct {
	RegionId   int    `gorm:"column:region_id;primaryKey" json:"region_id"`
	RegionName string `gorm:"column:region_name;type:varchar(255);not null" json:"region_name"`
}

func (Region) TableName() string {
	return "region"
}

// WarrantyReturn 保修退货
type WarrantyReturn struct {
	Id        int        `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	WarrantyId int       `gorm:"column:warranty_id;not null" json:"warranty_id"`
	Barcode   string     `gorm:"column:barcode;type:varchar(255);not null" json:"barcode"`
	ReturnAt  *time.Time `gorm:"column:return_at" json:"return_at"`
	CreatedAt time.Time  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt time.Time  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
	DeletedAt *time.Time `gorm:"column:deleted_at" json:"deleted_at"`
}

func (WarrantyReturn) TableName() string {
	return "warranty_return"
}
