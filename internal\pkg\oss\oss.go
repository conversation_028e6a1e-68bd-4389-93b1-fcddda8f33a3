package oss

import (
	"bytes"
	"fmt"
	"marketing-app/internal/pkg/config"
	"marketing-app/internal/pkg/log"
	"time"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/pkg/errors"
	"go.uber.org/zap"
)

// OSSService OSS服务接口
type OSSService interface {
	UploadFile(filename string, data []byte) error
	GetFileInfo(filename string) (*FileInfo, error)
	GetFullURL(filename string) string
}

// FileInfo 文件信息
type FileInfo struct {
	Key          string
	ETag         string
	ContentType  string
	Size         int64
	LastModified time.Time
}

// ossServiceImpl OSS服务实现
type ossServiceImpl struct {
	bucket *oss.Bucket
	config *config.OSSConfig
}

// NewOSSService 创建OSS服务实例
func NewOSSService() (OSSService, error) {
	ossConfig, err := config.GetOSSConfig()
	if err != nil {
		return nil, errors.Wrap(err, "获取OSS配置失败")
	}

	// 创建OSSClient实例
	client, err := oss.New(ossConfig.Endpoint, ossConfig.AccessID, ossConfig.AccessKey)
	if err != nil {
		return nil, errors.Wrap(err, "创建OSS客户端失败")
	}

	// 获取存储空间
	bucket, err := client.Bucket(ossConfig.Bucket)
	if err != nil {
		return nil, errors.Wrap(err, "获取OSS存储空间失败")
	}

	return &ossServiceImpl{
		bucket: bucket,
		config: ossConfig,
	}, nil
}

// UploadFile 上传文件到OSS
func (o *ossServiceImpl) UploadFile(filename string, data []byte) error {
	reader := bytes.NewReader(data)
	err := o.bucket.PutObject(filename, reader)
	if err != nil {
		return errors.Wrap(err, "上传文件到OSS失败")
	}

	log.Info("文件上传成功", zap.String("filename", filename))
	return nil
}

// GetFileInfo 获取文件信息
func (o *ossServiceImpl) GetFileInfo(filename string) (*FileInfo, error) {
	head, err := o.bucket.GetObjectDetailedMeta(filename)
	if err != nil {
		return nil, errors.Wrap(err, "获取文件信息失败")
	}

	// 构建FileInfo
	fileInfo := &FileInfo{
		Key:          filename,
		ETag:         head.Get("ETag"),
		ContentType:  head.Get("Content-Type"),
		Size:         0,
		LastModified: time.Time{},
	}

	// 解析Content-Length
	if contentLength := head.Get("Content-Length"); contentLength != "" {
		if _, err := fmt.Sscanf(contentLength, "%d", &fileInfo.Size); err != nil {
			log.Warn("解析Content-Length失败", zap.String("contentLength", contentLength), zap.Error(err))
		}
	}

	return fileInfo, nil
}

// GetFullURL 获取完整的文件访问URL
func (o *ossServiceImpl) GetFullURL(filename string) string {
	return o.config.CName + filename
}
