package model

// UserEndpoint represents the user-endpoint relationship in the database.
type UserEndpoint struct {
	UID      int    `gorm:"column:uid;not null;comment:'用户id,这个id来自admin_users表';uniqueIndex:uid_endpoint,uid"`
	Endpoint int    `gorm:"column:endpoint;not null;comment:'终端id';index:endpoint"`
	Role     string `gorm:"column:role;type:enum('manager','assistant','agency_manager');not null;comment:'角色，manager-店长，assistant-店员  agency_manager-代理店长'"`
	UserType string `gorm:"column:user_type;type:enum('user','visitor');not null;comment:'用户类型  user-用户，visitor-游客'"`
}

// TableName sets the insert table name for this struct type
func (UserEndpoint) TableName() string {
	return "user_endpoint"
}
