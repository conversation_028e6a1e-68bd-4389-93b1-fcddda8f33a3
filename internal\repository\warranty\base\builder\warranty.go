package builder

import (
	"gorm.io/gorm"
	"time"
)

type PurchaseDateRange struct {
	StartDate *time.Time `json:"buy_date_start"`
	EndDate   *time.Time `json:"buy_date_end"`
}

type Warranty struct {
	*PaginationParm
	*PurchaseDateRange
	ID            int64   `json:"id"`
	Uid           int     `json:"uid"`
	Barcode       string  `json:"barcode"`
	ExtBarcode    string  `json:"ext_barcode"`
	Endpoint      int     `json:"endpoint"`
	CustomerPhone string  `json:"customer_phone"`
	Number        string  `json:"number"`
	Imei          string  `json:"imei"`
	Model         string  `json:"model"`
	Phone         string  `json:"phone"`
	Salesman      string  `json:"salesman"`
	Status        []int64 `json:"status"`
	SalesmanId    int     `json:"salesman_id"`

	DoJoinEndpoint      string `json:"join_endpoint,omitempty"`
	DoJoinUserEndpoint  string `json:"join_user_endpoint,omitempty"`
	DoJoinMachineType   string `json:"join_machine_type,omitempty"`
	OrderBy             string `json:"order_by,omitempty"`
	BarcodeOrExtBarcode bool   `json:"barcode_or_ext_barcode,omitempty"`
}

func NewWarranty() *Warranty {
	var t time.Time
	return &Warranty{
		PaginationParm: &PaginationParm{},
		PurchaseDateRange: &PurchaseDateRange{
			StartDate: &t,
			EndDate:   &t,
		},
	}
}

func (w *Warranty) Fill(db *gorm.DB) *gorm.DB {
	db = w.fillWhere(db)
	if w.OrderBy != "" {
		db = db.Order(w.OrderBy)
	}
	if w.DoJoinEndpoint != "" {
		db = db.Joins(w.DoJoinEndpoint)
	}
	if w.DoJoinUserEndpoint != "" {
		db = db.Joins(w.DoJoinUserEndpoint)
	}
	if w.DoJoinMachineType != "" {
		db = db.Joins(w.DoJoinMachineType)
	}
	return db
}

func (w *Warranty) fillWhere(db *gorm.DB) *gorm.DB {
	if w.BarcodeOrExtBarcode {
		db = db.Where("barcode = ? OR ext_barcode = ?", w.Barcode, w.ExtBarcode)
	} else if w.Barcode != "" {
		db = db.Where("barcode = ?", w.Barcode)
	}
	if w.ID != 0 {
		db = db.Where("id = ?", w.ID)
	}
	if w.Number != "" {
		db = db.Where("number = ?", w.Number)
	}
	if w.Imei != "" {
		db = db.Where("imei = ?", w.Imei)
	}
	if w.Model != "" {
		db = db.Where("model = ?", w.Model)
	}
	if w.Phone != "" {
		db = db.Where("phone = ?", w.Phone)
	}
	if w.Salesman != "" {
		db = db.Where("salesman = ?", w.Salesman)
	}
	if w.Endpoint != 0 {
		db = db.Where("endpoint = ?", w.Endpoint)
	}
	if w.CustomerPhone != "" {
		db = db.Where("customer_phone = ?", w.CustomerPhone)
	}
	if len(w.Status) > 0 {
		db = db.Where("warranty.status in (?)", w.Status)
	}
	if !w.StartDate.IsZero() {
		db = db.Where("buy_date >= ?", w.StartDate)
	}
	if !w.EndDate.IsZero() {
		db = db.Where("buy_date < ?", w.EndDate)
	}
	if w.SalesmanId != 0 {
		db = db.Where("salesman_id = ?", w.SalesmanId)
	}
	if w.Uid != 0 {
		db = db.Where("user_endpoint.uid = ?", w.Uid)
	}
	return db
}

func (w *Warranty) IDs(v int64) *Warranty {
	w.ID = v
	return w
}

func (w *Warranty) BarcodeEq(v string) *Warranty {
	w.Barcode = v
	return w
}

func (w *Warranty) ExtBarcodeEq(v string) *Warranty {
	w.ExtBarcode = v
	return w
}

func (w *Warranty) Imeis(v string) *Warranty {
	w.Imei = v
	return w
}

func (w *Warranty) NumberEq(v string) *Warranty {
	w.Number = v
	return w
}

func (w *Warranty) ModelEq(v string) *Warranty {
	w.Model = v
	return w
}

func (w *Warranty) PhoneEq(v string) *Warranty {
	w.Phone = v
	return w
}

func (w *Warranty) Salesmen(v string) *Warranty {
	w.Salesman = v
	return w
}

func (w *Warranty) EndpointEq(v int) *Warranty {
	w.Endpoint = v
	return w
}

func (w *Warranty) CustomerPhoneEq(v string) *Warranty {
	w.CustomerPhone = v
	return w
}

func (w *Warranty) StatusIn(v ...int64) *Warranty {
	w.Status = v
	return w
}

func (w *Warranty) DateRange(v *PurchaseDateRange) *Warranty {
	if !v.StartDate.IsZero() {
		w.StartDate = v.StartDate
	}
	if !v.EndDate.IsZero() {
		w.EndDate = v.EndDate
	}
	return w
}

func (w *Warranty) DoOrderBy(v string) *Warranty {
	w.OrderBy = v
	return w
}

func (w *Warranty) JoinEndpoint(v string) *Warranty {
	w.DoJoinEndpoint = v
	return w
}

func (w *Warranty) JoinUserEndpoint(v string) *Warranty {
	w.DoJoinUserEndpoint = v
	return w
}

func (w *Warranty) JoinMachineType(v string) *Warranty {
	w.DoJoinMachineType = v
	return w
}

func (w *Warranty) UidEq(v int) *Warranty {
	w.Uid = v
	return w
}

func (w *Warranty) SalesmanIdEq(v int) *Warranty {
	w.SalesmanId = v
	return w
}

func (w *Warranty) Pagination(v *PaginationParm) *Warranty {
	w.Page = v.Page
	w.PageSize = v.PageSize
	return w
}

func (w *Warranty) BarcodeOrExtBarcodeEq(barcode string, extBarcode string) *Warranty {
	w.BarcodeOrExtBarcode = true
	w.Barcode = barcode
	w.ExtBarcode = extBarcode
	return w
}
