package builder

import (
	"gorm.io/gorm"
	"marketing-app/internal/model"
)

type Contact struct {
	Id            int
	EndpointId    int
	Name          string
	CustomerPhone string
	Warranty      *model.Warranty
}

func NewContact() *Contact {
	return &Contact{}
}

func (c *Contact) Fill(db *gorm.DB) *gorm.DB {
	db = c.fillWhere(db)
	return db
}

func (c *Contact) fillWhere(db *gorm.DB) *gorm.DB {
	if c.Id != 0 {
		db = db.Where("id = ?", c.Id)
	}
	if c.EndpointId != 0 {
		db = db.Where("endpoint_id = ?", c.EndpointId)
	}
	if c.Name != "" {
		db = db.Where("name = ?", c.Name)
	}
	if c.CustomerPhone != "" {
		db = db.Where("customer_phone = ?", c.CustomerPhone)
	}
	return db
}

func (c *Contact) IdEq(id int) *Contact {
	c.Id = id
	return c
}

func (c *Contact) NameEq(name string) *Contact {
	c.Name = name
	return c
}

func (c *Contact) CustomerPhoneEq(customerPhone string) *Contact {
	c.CustomerPhone = customerPhone
	return c
}

func (c *Contact) WarrantyEq(warranty *model.Warranty) *Contact {
	c.Warranty = warranty
	return c
}

func (c *Contact) EndpointIdEq(endpointId int) *Contact {
	c.EndpointId = endpointId
	return c
}
