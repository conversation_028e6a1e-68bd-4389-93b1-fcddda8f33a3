package config

import (
	"marketing-app/internal/pkg/config"
	"time"
)

type AppConfig struct {
	Host        string
	AppID       string
	AppKey      string
	HTTPTimeout time.Duration
}

func LoadBrokenScreenConfig() *AppConfig {
	return &AppConfig{
		Host:        config.GetString("repair.base_url"),
		AppID:       config.GetString("repair.app_id"),
		AppKey:      config.GetString("repair.app_key"),
		HTTPTimeout: 10 * time.Second,
	}
}
