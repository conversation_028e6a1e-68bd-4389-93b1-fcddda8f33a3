package builder

import "gorm.io/gorm"

type MesDevices struct {
	Barcode string `json:"barcode"`
	Imei    string `json:"imei"`
	Number  string `json:"number"`
	State   []int  `json:"status_in"`

	OrderBy string `json:"order_by"`
}

func NewMesDevices() *MesDevices {
	return &MesDevices{}
}

func (m *MesDevices) Fill(db *gorm.DB) *gorm.DB {
	db = m.fillWhere(db)
	if m.OrderBy != "" {
		db = db.Order(m.OrderBy)
	}
	return db
}

func (m *MesDevices) fillWhere(db *gorm.DB) *gorm.DB {
	if m.Barcode != "" {
		db = db.Where("barcode = ?", m.Barcode)
	}
	if m.Imei != "" {
		db = db.Where("imei = ?", m.Imei)
	}
	if m.Number != "" {
		db = db.Where("number = ?", m.Number)
	}
	if len(m.State) > 0 {
		db = db.Where("state in (?)", m.State)
	}
	return db
}

func (m *MesDevices) BarcodeEq(v string) *MesDevices {
	m.Barcode = v
	return m
}

func (m *MesDevices) NumberEq(v string) *MesDevices {
	m.Number = v
	return m
}

func (m *MesDevices) StateIn(v ...int) *MesDevices {
	m.State = v
	return m
}

func (m *MesDevices) DoOrderBy(v string) *MesDevices {
	m.OrderBy = v
	return m
}
