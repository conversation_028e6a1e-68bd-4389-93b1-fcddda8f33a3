package builder

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type OwnOrder struct {
	Id         int
	Model      string
	EndpointId int
	Mobile     string
	WarrantyId int
	OrderBy    string

	ForUpdateLock bool
}

func NewOwnOrder() *OwnOrder {
	return &OwnOrder{}
}

func (o *OwnOrder) Fill(db *gorm.DB) *gorm.DB {
	db = o.fillWhere(db)
	if o.OrderBy != "" {
		db.Order(o.OrderBy)
	}
	return db
}

func (o *OwnOrder) fillWhere(db *gorm.DB) *gorm.DB {
	if o.Id != 0 {
		db = db.Where("id = ?", o.Id)
	}
	if o.EndpointId != 0 {
		db.Where("endpoint_id = ?", o.EndpointId)
	}
	if o.WarrantyId != 0 {
		db.Where("warranty_id = ?", o.WarrantyId)
	}
	if o.Model != "" {
		db.Where("model = ?", o.Model)
	}
	if o.Mobile != "" {
		db = db.Where("mobile = ?", o.Mobile)
	}
	if o.ForUpdateLock {
		db = db.Clauses(clause.Locking{Strength: clause.LockingStrengthUpdate})
	}
	return db
}

func (o *OwnOrder) IdEq(v int) *OwnOrder {
	o.Id = v
	return o
}

func (o *OwnOrder) ModelEq(v string) *OwnOrder {
	o.Model = v
	return o
}

func (o *OwnOrder) EndpointIdEq(v int) *OwnOrder {
	o.EndpointId = v
	return o
}

func (o *OwnOrder) MobileEq(v string) *OwnOrder {
	o.Mobile = v
	return o
}

func (o *OwnOrder) WarrantyIdEq(v int) *OwnOrder {
	o.WarrantyId = v
	return o
}

func (o *OwnOrder) DoOrderBy(v string) *OwnOrder {
	o.OrderBy = v
	return o
}

func (o *OwnOrder) ForUpdate() *OwnOrder {
	o.ForUpdateLock = true
	return o
}
