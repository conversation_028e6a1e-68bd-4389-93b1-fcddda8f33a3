package common

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing-app/internal/model"
	"marketing-app/internal/repository/common/builder"
	"marketing-app/internal/repository/common/dto"
)

type MesDevice interface {
	GetMesDevices(c *gin.Context, query *builder.MesDevices) (data *dto.MesDevices, err error)
}

type mesDevices struct {
	db *gorm.DB
}

func NewMesDevices(db *gorm.DB) MesDevice {
	return &mesDevices{
		db: db,
	}
}

func (m *mesDevices) GetMesDevices(c *gin.Context, query *builder.MesDevices) (data *dto.MesDevices, err error) {
	err = query.Fill(m.db.WithContext(c).Model(&model.MesDevices{})).
		Select("mes_devices.*, IF(mes_devices.procedure = 4, 1, 0) as status, mes_devices.update_time as product_date").
		Find(&data).Error
	return
}
