package dto

// PrototypeUserInfo 样机演示用户信息
type PrototypeUserInfo struct {
	ID    int    `json:"id"`    // 用户ID
	Phone string `json:"phone"` // 手机号
}

// PrototypeUserDetail 样机演示用户详细信息
type PrototypeUserDetail struct {
	ID           int    `json:"id"`            // 用户ID
	TopAgency    *int   `json:"top_agency"`    // 一级代理id
	SecondAgency *int   `json:"second_agency"` // 二级代理id
	Endpoint     *int   `json:"endpoint"`      // 终端id
	Phone        string `json:"phone"`         // 手机号
}

// PrototypeUserBind 终端演示用户绑定结果
type PrototypeUserBind struct {
	ID         int    `json:"id"`          // 用户ID
	Phone      string `json:"phone"`       // 手机号
	EndpointID int    `json:"endpoint_id"` // 终端ID
}
