package validator

import (
	"errors"
	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/locales/zh"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	zhtranslations "github.com/go-playground/validator/v10/translations/zh"
	"log"
	"regexp"
)

var trans ut.Translator
var validate *validator.Validate

// 自定义手机号码校验器（如果提供了手机号，则进行检查）
var phoneRegex = regexp.MustCompile(`^1[3-9]\d{9}$`) // 中国大陆手机号的正则表达式

var phoneValidator validator.Func = func(fl validator.FieldLevel) bool {
	phone := fl.Field().String()

	if len(phone) == 0 {
		return true
	}
	// 基本验证
	if len(phone) != 11 {
		// 不要尝试设置字段值，而是返回 false
		return false
	}

	// 正则验证
	if !phoneRegex.MatchString(phone) {
		return false
	}

	return true
}

var httpMethodValidator validator.Func = func(fl validator.FieldLevel) bool {
	methods, ok := fl.Field().Interface().([]string)
	if !ok {
		return false // 如果不是字符串切片，则验证失败
	}

	// 定义允许的 HTTP 方法
	validMethods := map[string]bool{
		"GET":    true,
		"POST":   true,
		"PUT":    true,
		"DELETE": true,
		"PATCH":  true,
	}

	// 遍历方法数组并进行验证
	for _, method := range methods {
		if _, valid := validMethods[method]; !valid {
			return false // 如果有无效的方法，返回 false
		}
	}
	return true // 所有方法都是有效的
}

func Init() {
	uni := ut.New(zh.New())
	trans, _ = uni.GetTranslator("zh")
	validate = binding.Validator.Engine().(*validator.Validate)

	// 注册自定义手机号验证器
	err := validate.RegisterValidation("phone", phoneValidator)
	if err != nil {
		log.Fatal(err)
	}

	// 注册手机号验证的错误信息翻译
	err = validate.RegisterTranslation("phone", trans,
		func(ut ut.Translator) error {
			return ut.Add("phone", "{0}必须是有效的手机号码", true)
		},
		func(ut ut.Translator, fe validator.FieldError) string {
			t, _ := ut.T("phone", fe.Field())
			return t
		},
	)
	if err != nil {
		log.Fatal(err)
	}

	// 注册自定义http_method验证器
	err = validate.RegisterValidation("http_method", httpMethodValidator)
	if err != nil {
		log.Fatal(err)
	}

	// 注册 http_method 验证的错误信息翻译
	err = validate.RegisterTranslation("http_method", trans,
		func(ut ut.Translator) error {
			return ut.Add("http_method", "{0}必须是有效的HTTP方法(GET, POST, PUT, DELETE)", true)
		},
		func(ut ut.Translator, fe validator.FieldError) string {
			t, _ := ut.T("http_method", fe.Field())
			return t
		},
	)
	if err != nil {
		log.Fatal(err)
	}

	//注册默认翻译器
	err = zhtranslations.RegisterDefaultTranslations(validate, trans)
	if err != nil {
		log.Fatal(err)
	}
}

func Translate(err error) map[string][]string {
	var result = make(map[string][]string)
	var errs validator.ValidationErrors
	errors.As(err, &errs)
	for _, err := range errs {
		result[err.Field()] = append(result[err.Field()], err.Translate(trans))
	}
	return result
}
