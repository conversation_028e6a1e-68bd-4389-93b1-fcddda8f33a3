package warranty_convertor

import "sync"

var (
	warrantyBaseConvertor *WarrantyBaseConvertor
	warrantyBaseOnce      sync.Once
)

func NewWarrantyBaseConvertor() *WarrantyBaseConvertor {
	warrantyBaseOnce.Do(func() {
		warrantyBaseConvertor = new(WarrantyBaseConvertor)
	})
	return warrantyBaseConvertor
}

var (
	identifierConvertor *IdentifierConvertor
	identifierOnce      sync.Once
)

func NewIdentifierConvertor() *IdentifierConvertor {
	identifierOnce.Do(func() {
		identifierConvertor = new(IdentifierConvertor)
	})
	return identifierConvertor
}

var (
	warrantyEntryConvertor *WarrantyEntryConvertor
	warrantyEntryOnce      sync.Once
)

func NewWarrantyEntryConvertor() *WarrantyEntryConvertor {
	warrantyEntryOnce.Do(func() {
		warrantyEntryConvertor = new(WarrantyEntryConvertor)
	})
	return warrantyEntryConvertor
}

var (
	warrantyReturnConvertor *WarrantyReturnConvertor
	warrantyReturnOnce      sync.Once
)

func NewWarrantyReturnConvertor() *WarrantyReturnConvertor {
	warrantyReturnOnce.Do(func() {
		warrantyReturnConvertor = new(WarrantyReturnConvertor)
	})
	return warrantyReturnConvertor
}

var (
	warrantyExchangeConvertor *WarrantyExchangeConvertor
	warrantyExchangeOnce      sync.Once
)

func NewWarrantyExchangeConvertor() *WarrantyExchangeConvertor {
	warrantyExchangeOnce.Do(func() {
		warrantyExchangeConvertor = new(WarrantyExchangeConvertor)
	})
	return warrantyExchangeConvertor
}
