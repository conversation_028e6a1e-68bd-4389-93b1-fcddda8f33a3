package base

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing-app/internal/model"
	"marketing-app/internal/repository/warranty/base/builder"
	"marketing-app/internal/repository/warranty/base/dto"
)

type Warranty interface {
	GetList(c *gin.Context, query *builder.Warranty) (list []dto.WarrantyBase, total int64, err error)
	GetDetails(c *gin.Context, query *builder.Warranty) (details []dto.WarrantyDetail, total int64, err error)

	GetWarrantyMachineType(c *gin.Context, query *builder.Warranty) (data dto.WarrantyMachineType, err error)
}

type warranty struct {
	db *gorm.DB
}

func NewWarranty(db *gorm.DB) Warranty {
	return &warranty{
		db: db,
	}
}

func (w *warranty) GetList(c *gin.Context, query *builder.Warranty) (list []dto.WarrantyBase, total int64, err error) {
	rawWarranty := query.Fill(w.db.WithContext(c).Model(&model.Warranty{}))
	rawWarranty.Count(&total)
	err = rawWarranty.Scopes(builder.Paginate(query.Page, query.PageSize)).Scan(&list).Error
	if err != nil {
		return nil, 0, err
	}
	return
}

func (w *warranty) GetDetails(c *gin.Context, query *builder.Warranty) (details []dto.WarrantyDetail, total int64, err error) {
	rawWarranty := query.Fill(w.db.WithContext(c).Model(&model.Warranty{}).Select("warranty.*, warranty.created_at as created_at, warranty.endpoint as endpoint_id, endpoint.name as endpoint_name, endpoint.address as endpoint_address, endpoint.phone as endpoint_phone, endpoint.manager as endpoint_manager"))
	rawWarranty.Count(&total)
	err = rawWarranty.Scan(&details).Error
	if err != nil {
		return nil, 0, err
	}
	return
}

func (w *warranty) GetWarrantyMachineType(c *gin.Context, query *builder.Warranty) (data dto.WarrantyMachineType, err error) {
	err = query.Fill(w.db.WithContext(c).Model(&model.Warranty{})).Select("warranty.*, mt.category_id").Find(&data).Error
	return
}
