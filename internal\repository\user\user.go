package user

import (
	"errors"
	"marketing-app/internal/model"
	"marketing-app/internal/repository/user/dto"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type User interface {
	GetAgencyInfo(c *gin.Context, uid int) (*dto.AgencyInfo, error)
	VerifyPhoneCode(c *gin.Context, code, phone string, minute int) (int, error)
}

type user struct {
	db *gorm.DB
}

func NewUser(db *gorm.DB) User {
	return &user{db: db}
}

// GetAgencyInfo 获取代理商信息
func (u *user) GetAgencyInfo(c *gin.Context, uid int) (*dto.AgencyInfo, error) {
	var agencyInfo model.UserAgency
	err := u.db.WithContext(c).
		Where("uid = ? AND status = 1", uid).
		First(&agencyInfo).Error
	if err != nil {
		return nil, err
	}

	// 转换为DTO
	result := &dto.AgencyInfo{
		TopAgency:    agencyInfo.TopAgency,
		SecondAgency: agencyInfo.SecondAgency,
	}

	return result, nil
}

// VerifyPhoneCode 验证手机验证码
func (u *user) VerifyPhoneCode(c *gin.Context, code, phone string, minute int) (int, error) {
	var phoneCode model.PhoneCode
	err := u.db.WithContext(c).
		Where("phone = ?", phone).
		First(&phoneCode).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return -1, nil // 验证码不存在
		}
		return 0, err
	}

	// 检查验证码是否匹配
	if phoneCode.Code != code {
		return -2, nil // 验证码不正确
	}

	// 检查验证码是否过期
	expireTime := phoneCode.UpdatedAt.Add(time.Duration(minute) * time.Minute)
	if time.Now().After(expireTime) {
		return 2, nil // 验证码已过期
	}

	return 1, nil // 验证成功
}
