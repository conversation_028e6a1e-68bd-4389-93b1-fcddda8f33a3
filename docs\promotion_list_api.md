# 促销列表 API 文档

## 概述

促销列表API提供了三个主要接口，按照通知模块的结构模式重新组织：
1. 获取促销活动列表
2. 获取促销活动详情
3. 上传回执

## 文件结构

- `internal/handler/promotion_list.go` - HTTP请求处理器
- `internal/service/promotion_list.go` - 业务逻辑服务
- `internal/repository/promotion_list.go` - 数据访问层
- `internal/handler/dto/promotion_list.go` - 数据传输对象
- `internal/router/promotion_list/promotion_list.go` - 路由定义

## API 接口

### 1. 获取促销活动列表

**接口地址：** `GET /apps/v1/app/promotion/list`

**请求参数：**
```json
{
  "id": 1,           // 必填，促销活动ID
  "agency": 0,       // 可选，代理商ID
  "endpoint": 0,     // 可选，终端ID（终端用户会自动设置）
  "receipt": 0,      // 可选，回执状态 1-已上传 2-未上传
  "model_id": [],    // 可选，机型ID列表
  "keyword": "",     // 可选，关键词搜索(条码)
  "page": 1,         // 必填，页码
  "page_size": 10    // 必填，每页数量
}
```

**响应示例：**
```json
{
  "ok": 1,
  "msg": "ok",
  "data": {
    "list": [
      {
        "id": 1,
        "sales_promotion_id": 1,
        "endpoint": 1,
        "model_id": 1,
        "barcode": "ABC123456",
        "buy_date": "2024-01-01T00:00:00Z",
        "warranty_id": 1,
        "is_receipt": 1,
        "receipt_at": "2024-01-02T00:00:00Z",
        "region_name": "广东深圳",
        "agency_name": "代理商名称",
        "endpoint_name": "终端名称",
        "endpoint_code": "EP001",
        "manager": "张三",
        "endpoint_phone": "13800138000",
        "address": "深圳市南山区",
        "receipt": "image1.jpg,image2.jpg",
        "number": "SN123456",
        "activated_at": "2024-01-01 10:00:00",
        "student_name": "学生姓名"
      }
    ],
    "total": 100,
    "page": 1,
    "page_size": 10
  }
}
```

### 2. 获取促销活动详情

**接口地址：** `GET /apps/v1/app/promotion/list/{id}`

**路径参数：**
- `id`: 促销活动列表ID

**响应示例：**
```json
{
  "ok": 1,
  "msg": "ok",
  "data": {
    "id": 1,
    "sales_promotion_id": 1,
    "endpoint": 1,
    "model_id": 1,
    "barcode": "ABC123456",
    "buy_date": "2024-01-01T00:00:00Z",
    "warranty_id": 1,
    "is_receipt": 1,
    "receipt_at": "2024-01-02T00:00:00Z",
    "student_uid": "student123",
    "student_name": "学生姓名",
    "agency_name": "代理商名称",
    "activated_at": "2024-01-01 10:00:00",
    "number": "SN123456",
    "hour_interval": 2,
    "endpoint_code": "EP001",
    "endpoint_name": "终端名称",
    "manager": "张三",
    "return_at": null,
    "receipt": "image1.jpg,image2.jpg"
  }
}
```

### 3. 上传回执

**接口地址：** `POST /apps/v1/app/promotion/list/{id}`

**路径参数：**
- `id`: 促销活动列表ID

**请求体：**
```json
{
  "receipt": "image1.jpg,image2.jpg"  // 必填，回执图片，多个用逗号分隔
}
```

**响应示例：**
```json
{
  "ok": 1,
  "msg": "ok",
  "data": {
    "message": "上传成功"
  }
}
```

## 错误响应

当请求出错时，API会返回以下格式的错误响应：

```json
{
  "ok": 0,
  "msg": "错误信息"
}
```

常见错误：
- `bind request error`: 请求参数格式错误
- `unauthorized`: 未授权访问
- `记录不存在`: 指定的记录不存在
- `无权操作`: 没有权限操作该记录
- `回执上传已经过了截止时间`: 超过了回执上传的截止时间

## 权限说明

- **终端用户**：只能查看和操作自己终端的数据
- **后台用户**：可以查看所有数据，但不能上传回执

## 数据库表结构

### sales_promotion_list (促销活动列表)
- `id`: 主键
- `sales_promotion_id`: 促销活动ID
- `endpoint`: 终端ID
- `model_id`: 机型ID
- `barcode`: 条码
- `buy_date`: 购买日期
- `warranty_id`: 保修ID
- `is_receipt`: 是否已上传回执
- `receipt_at`: 回执上传时间

### sales_promotion_list_receipt (促销活动回执)
- `id`: 主键
- `sales_promotion_list_id`: 促销活动列表ID
- `receipt`: 回执图片路径
- `number`: 编号
- `type`: 类型

### sales_promotion (促销活动)
- `id`: 主键
- `name`: 活动名称
- `receipt_day`: 回执截止日期

## 注意事项

1. 所有接口都需要通过认证中间件
2. 终端用户的endpoint信息会自动从认证信息中获取
3. 回执上传有时间限制，超过截止时间后无法上传
4. 图片路径需要包含完整的文件路径信息
