package model

import (
	"gorm.io/gorm"
	"time"
)

type DrpRecords struct {
	ID               int            `gorm:"column:id;primaryKey;autoIncrement"`
	UID              int            `gorm:"column:uid;not null;default:0;comment:操作人"`
	Warehouse        int            `gorm:"column:warehouse;not null;comment:仓库"`
	NewWarehouse     int            `gorm:"column:new_warehouse;not null;default:0;comment:仓库"`
	ManageWarehouse  int            `gorm:"column:manage_warehouse;not null;default:0;comment:管理方仓库"`
	RecordType       int            `gorm:"column:record_type;default:1;comment:记录类型   1--入库   2--离库   3--盘点  4--调拨  【5--入样机   6--录入保卡（已售）  7--换机   8--退机】  9--发货 10--调货"`
	Type             int            `gorm:"column:type;default:0;comment:操作类型   1、采购   2、换货    3、退货   4、发货   5-调货"`
	Status           int            `gorm:"column:status;default:1;comment:状态   0 --未完成  1--正常   -1异常(补充状态)"`
	BillID           *int           `gorm:"column:bill_id;unique;comment:金蝶单据ID"`
	Remark           string         `gorm:"column:remark;size:255;comment:备注"`
	CreatedAt        *time.Time     `gorm:"column:created_at;comment:创建时间"`
	UpdatedAt        *time.Time     `gorm:"column:updated_at;comment:更新时间"`
	DeletedAt        gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间"`
	TransferMachines string         `gorm:"column:transfer_machines;type:text;comment:调货商品 机型  数量  json数组"`
	ShipperUID       *int           `gorm:"column:shipper_uid;comment:发货方uid"`
	ShipperRemark    string         `gorm:"column:shipper_remark;size:255;comment:发货方备注"`
}

func (DrpRecords) TableName() string {
	return "drp_records"
}
