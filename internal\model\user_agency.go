package model

import "time"

// UserAgency 代理用户表
// 该表可以手动建立总代和二代的账户,也有大区经理和领导帐号这些
type UserAgency struct {
	ID           int        `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	UID          int        `gorm:"column:uid;not null;comment:用户id,这个id来自admin_users表" json:"uid"`
	TopAgency    *int       `gorm:"column:top_agency;comment:一级代理id" json:"top_agency"`
	SecondAgency int        `gorm:"column:second_agency;default:0;comment:二级代理id" json:"second_agency"`
	Partition    *int       `gorm:"column:partition;comment:大区id" json:"partition"`
	RoleID       int        `gorm:"column:role_id;not null;default:0;comment:角色id,8是活动管理员,拥有管理员的权限,9是大区经理,10是总代" json:"role_id"`
	WeixinID     *string    `gorm:"column:weixin_id;comment:微信昵称" json:"weixin_id"`
	Remark       string     `gorm:"column:remark;type:text;not null;comment:备注" json:"remark"`
	Email        string     `gorm:"column:email;size:60;not null;comment:邮箱" json:"email"`
	UpdatedAt    *time.Time `gorm:"column:updated_at" json:"updated_at"`
	CreatedAt    *time.Time `gorm:"column:created_at" json:"created_at"`
	Status       int8       `gorm:"column:status;not null;default:1;comment:状态,0为禁用,1为启用" json:"status"`
	Phone        *string    `gorm:"column:phone;size:50;comment:电话号码" json:"phone"`
}

// TableName 指定表名
func (UserAgency) TableName() string {
	return "user_agency"
}
