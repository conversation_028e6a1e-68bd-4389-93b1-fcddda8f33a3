package repository

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing-app/internal/model"
)

type PhoneCodeRepository interface {
	// GetPhoneCode 获取手机验证码
	GetPhoneCode(c *gin.Context, phone, code string) (*model.PhoneCode, error)
	// SetPhoneCode 设置手机验证码
	SetPhoneCode(c *gin.Context, phone, code string) error
	// ConsumePhoneCode 更新验证码使用
	ConsumePhoneCode(c *gin.Context, phone string) error
}

type phoneCodeRepository struct {
	// db 连接
	db *gorm.DB
}

func NewPhoneCodeRepository(db *gorm.DB) PhoneCodeRepository {
	return &phoneCodeRepository{
		db: db,
	}
}

func (r *phoneCodeRepository) GetPhoneCode(c *gin.Context, phone, code string) (*model.PhoneCode, error) {
	var phoneCode model.PhoneCode
	err := r.db.WithContext(c).Model(&model.PhoneCode{}).Where("phone = ? AND code = ?", phone, code).First(&phoneCode).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 如果没有找到记录，返回 nil
		}
		return nil, err
	}
	return &phoneCode, nil
}

func (r *phoneCodeRepository) SetPhoneCode(c *gin.Context, phone, code string) error {
	phoneCode := model.PhoneCode{
		Phone: phone,
		Code:  code,
	}
	err := r.db.WithContext(c).Model(&model.PhoneCode{}).Create(&phoneCode).Error
	if err != nil {
		return err
	}
	return nil
}

func (r *phoneCodeRepository) ConsumePhoneCode(c *gin.Context, phone string) error {

	err := r.db.WithContext(c).Model(&model.PhoneCode{}).Where("phone = ?", phone).Update("consume", 1).Error
	if err != nil {
		return err
	}
	return nil
}
