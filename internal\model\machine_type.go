package model

import (
	"time"
)

// MachineType 机型表
type MachineType struct {
	Id                int     `json:"id" gorm:"column:id"`                                   // id
	Name              string  `json:"name" gorm:"column:name"`                               // 型号
	NavName           string  `json:"nav_name" gorm:"column:nav_name"`                       // 实销机型名称
	ModelId           int     `json:"model_id" gorm:"column:model_id"`                       // 机型id
	ModelName         string  `json:"model_name" gorm:"column:model_name"`                   // 固件机型
	CategoryId        int     `json:"category_id" gorm:"column:category_id"`                 // 分类id
	CategoryName      string  `json:"category_name" gorm:"column:-"`                         // 分类名称
	CompanyPrice      float64 `json:"company_price" gorm:"column:company_price"`             // 公司售价
	TopAgencyPrice    float64 `json:"top_agency_price" gorm:"column:top_agency_price"`       // 总代售价
	SecondAgencyPrice float64 `json:"second_agency_price" gorm:"column:second_agency_price"` // 二代售价
	CustomerPrice     float64 `json:"customer_price" gorm:"column:customer_price"`           // 顾客售价
	ChartShow         int     `json:"chart_show" gorm:"column:chart_show"`                   // 实销统计是否展示 0:不展示 1:展示
	PrototypeStatus   int     `json:"prototype_status" gorm:"column:prototype_status"`       // 可作样机 0:不可以 1:可以
	PrototypeApkPath  string  `json:"prototype_apk_path" gorm:"column:prototype_apk_path"`   // 演示Apk地址
	VersionCode       string  `json:"version_code" gorm:"column:version_code"`               // Apk版本号
	ExtBarcodeNum     int     `json:"ext_barcode_num" gorm:"column:ext_barcode_num"`         // 副机条码数量
	Declare           int     `json:"declare" gorm:"column:declare"`                         // 允许申报 0:不允许 1:允许
	Stock             int     `json:"stock" gorm:"column:stock"`                             // 允许备货 0:不允许 1:允许
	Visibility        int     `json:"visibility" gorm:"column:visibility"`                   // 是否隐藏 0:隐藏 1:不隐藏
}

func (MachineType) TableName() string {
	return "machine_type"
}

// RepairMachineType 维修机型表
type RepairMachineType struct {
	Id           int       `json:"id" gorm:"column:id"`                       // id
	ModelId      int       `json:"model_id" gorm:"column:model_id"`           // 机型id
	ModelName    string    `json:"model_name" gorm:"column:model_name"`       // 固件机型
	CategoryId   int       `json:"category_id" gorm:"column:category_id"`     // 分类id
	CategoryName string    `json:"category_name" gorm:"column:category_name"` // 分类名称
	Visibility   int       `json:"visibility" gorm:"column:visibility"`       // 是否隐藏 0:隐藏 1:不隐藏
	CreatedAt    time.Time `json:"-" gorm:"created_at"`                       // CreatedAt 创建时间
	CreateTime   string    `json:"create_time" gorm:"-"`                      // 创建时间
}

func (RepairMachineType) TableName() string {
	return "machine_type"
}
