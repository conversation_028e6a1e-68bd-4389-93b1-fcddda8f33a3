package model

import (
	"gorm.io/gorm"
	"time"
)

type DrpMachine struct {
	ID           int            `gorm:"primaryKey;autoIncrement"`
	ModelID      int            `gorm:"column:model_id;comment:机型id"`
	Model        string         `gorm:"column:model;size:32;comment:机型"`
	Barcode      string         `gorm:"column:barcode;size:32;not null;uniqueIndex:barcode_warehouse_id;uniqueIndex:barcode_warehouse_id_status;comment:条码"`
	Number       string         `gorm:"column:number;size:48;not null;default:'';comment:序列码"`
	IMEI         string         `gorm:"column:imei;size:15;not null;default:'';comment:imei码"`
	TopAgency    int            `gorm:"column:top_agency;default:0;comment:一级代理"`
	SecondAgency int            `gorm:"column:second_agency;default:0;comment:二级代理"`
	Endpoint     int            `gorm:"column:endpoint;default:0;comment:终端、销售网点id"`
	WarehouseID  int            `gorm:"column:warehouse_id;not null;uniqueIndex:barcode_warehouse_id;uniqueIndex:barcode_warehouse_id_status;comment:仓库id"`
	Status       int            `gorm:"column:status;default:1;comment:0--未生效（盘盈）1--在库  2--离库  3--销售（保卡)  4--自拆样机 5--换机样机  6--退机样机"`
	SubStatus    int            `gorm:"column:sub_status;default:1;comment:0--未生效（盘盈）1--采购  2--换货  3--退货 4--调拨  5--样机  6--保卡"`
	CreatedAt    *time.Time     `gorm:"column:created_at;comment:创建时间"`
	UpdatedAt    *time.Time     `gorm:"column:updated_at;comment:更新时间"`
	DeletedAt    gorm.DeletedAt `gorm:"column:deleted_at;index;comment:删除时间"`
}

// TableName sets the insert table name for struct
func (DrpMachine) TableName() string {
	return "drp_machines"
}
