package model

import (
	"time"
)

// Grade 年级表
type Grade struct {
	Id         int       `json:"id" gorm:"column:id;primaryKey;autoIncrement"`                                                // 主键ID
	Name       string    `json:"name" gorm:"column:name;type:varchar(100);comment:科目名称"`                                      // 科目名称
	Stage      int       `json:"stage" gorm:"column:stage;type:tinyint;default:1;not null"`                                   // 学习阶段
	Order      int       `json:"order" gorm:"column:order;not null"`                                                          // 排序
	Visibility int       `json:"visibility" gorm:"column:visibility;type:tinyint;default:1;not null"`                         // 可见性
	CreatedAt  time.Time `json:"created_at" gorm:"column:created_at;type:timestamp;default:NULL"`                             // 创建时间
	UpdatedAt  time.Time `json:"updated_at" gorm:"column:updated_at;type:timestamp;default:NULL ON UPDATE CURRENT_TIMESTAMP"` // 更新时间
}

// TableName 指定表名
func (Grade) TableName() string {
	return "grade"
}
