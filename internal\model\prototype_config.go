package model

import (
	"time"
)

type PrototypeConfig struct {
	ID               int        `json:"id,omitempty"`
	ModelID          int        `json:"model_id" gorm:"column:model_id;primaryKey"`
	Model            string     `json:"model"`
	Discontinued     int        `json:"discontinued"` // 0:未下线 1:已下线
	LaunchedDate     *time.Time `json:"launched_date"`
	DiscontinuedDate *time.Time `json:"discontinued_date"`
	CreatedAt        string     `json:"created_at,omitempty"`
	UpdatedAt        string     `json:"updated_at,omitempty"`
}
