package warranty_homepage

import (
	"errors"
	"github.com/gin-gonic/gin"
	"marketing-app/internal/repository/warranty/base/dto"
)

type SearchRequest struct {
	Context    *gin.Context
	Query      string
	EndpointId int
}

type Searcher interface {
	SetNext(Searcher) Searcher
	Find(req *SearchRequest) (data []dto.WarrantyDetail, total int64, err error)
}

type BaseSearcher struct {
	next Searcher
}

func (b *BaseSearcher) SetNext(next Searcher) Searcher {
	b.next = next
	return next
}

func (b *BaseSearcher) Find(req *SearchRequest) (data []dto.WarrantyDetail, total int64, err error) {
	if b.next != nil {
		return b.next.Find(req)
	}
	return nil, 0, errors.New("error not found")
}
