package builder

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type Warranty struct {
	Barcode       string      `json:"barcode"`
	ExtBarcode    string      `json:"ext_barcode"`
	Imei          string      `json:"imei"`
	Number        string      `json:"number"`
	CustomerPhone string      `json:"customer_phone"`
	Status        []int       `json:"status"`
	Omits         []string    `json:"omits"`
	RawWhereSQL   clause.Expr `json:"raw_where"`

	CategoryId          int    `json:"category_id"`
	BarcodeOrExtBarcode bool   `json:"barcode_or_ext_barcode"`
	DoJoinMachineType   string `json:"join_machine_type"`
	CreatedAtNotNull    bool   `json:"created_at_not_null"`
}

func NewWarranty() *Warranty {
	return &Warranty{}
}

func (w *Warranty) Fill(db *gorm.DB) *gorm.DB {
	db = w.fillWhere(db)
	if w.DoJoinMachineType != "" {
		db.Jo<PERSON>(w.<PERSON>nMachineType)
	}
	if len(w.Omits) > 0 {
		db = db.Omit(w.Omits...)
	}
	return db
}

func (w *Warranty) fillWhere(db *gorm.DB) *gorm.DB {
	if w.BarcodeOrExtBarcode {
		db = db.Where("barcode = ? OR ext_barcode = ?", w.Barcode, w.ExtBarcode)
	} else if w.Barcode != "" {
		db = db.Where("barcode = ?", w.Barcode)
	}
	if w.Imei != "" {
		db = db.Where("imei = ?", w.Imei)
	}
	if w.Number != "" {
		db = db.Where("number = ?", w.Number)
	}
	if w.CustomerPhone != "" {
		db = db.Where("customer_phone = ?", w.CustomerPhone)
	}
	if w.CategoryId != 0 {
		db = db.Where("mt.category_id = ?", w.CategoryId)
	}
	if len(w.Status) > 0 {
		db = db.Where("status in (?)", w.Status)
	}
	if w.RawWhereSQL.SQL != "" {
		db = db.Where(w.RawWhereSQL)
	}
	if w.CreatedAtNotNull {
		db = db.Where("warranty.created_at is not NULL")
	}
	return db
}

func (w *Warranty) BarcodeEq(v string) *Warranty {
	w.Barcode = v
	return w
}

func (w *Warranty) ImeiEq(v string) *Warranty {
	w.Imei = v
	return w
}

func (w *Warranty) NumberEq(v string) *Warranty {
	w.Number = v
	return w
}

func (w *Warranty) CustomerPhoneEq(v string) *Warranty {
	w.CustomerPhone = v
	return w
}

func (w *Warranty) StatusIn(v ...int) *Warranty {
	w.Status = v
	return w
}

func (w *Warranty) RawWhere(v clause.Expr) *Warranty {
	w.RawWhereSQL = v
	return w
}

func (w *Warranty) JoinMachineType(v string) *Warranty {
	w.DoJoinMachineType = v
	return w
}

func (w *Warranty) CreatedAtIsNotNull() *Warranty {
	w.CreatedAtNotNull = true
	return w
}

func (w *Warranty) BarcodeOrExtBarcodeEq(barcode string, extBarcode string) *Warranty {
	w.BarcodeOrExtBarcode = true
	w.Barcode = barcode
	w.ExtBarcode = extBarcode
	return w
}

func (w *Warranty) DoOmit(v ...string) *Warranty {
	w.Omits = v
	return w
}

func (w *Warranty) CategoryIdEq(v int) *Warranty {
	w.CategoryId = v
	return w
}
