package warranty_return

import (
	"github.com/gin-gonic/gin"
	"marketing-app/internal/cache"
	"marketing-app/internal/handler/warranty/warranty_return"
	"marketing-app/internal/pkg/db"
	contactRepo "marketing-app/internal/repository/contact"
	endpointRepo "marketing-app/internal/repository/endpoint/endpoint"
	userEndpointRepo "marketing-app/internal/repository/endpoint/user_endpoint"
	machineRepo "marketing-app/internal/repository/machine"
	prototypeRepo "marketing-app/internal/repository/prototype"
	"marketing-app/internal/repository/warranty/returns"
	returnsService "marketing-app/internal/service/warranty/warranty_return"
)

type WarrantyReturnRouter struct {
	warrantyReturnHandler warranty_return.WarrantyReturnHandler
}

func NewWarrantyReturnRouter() *WarrantyReturnRouter {
	database, _ := db.GetDB()
	repo := returns.NewWarranty(database)
	ueRepo := userEndpointRepo.NewUserEndpoint(database)
	epRepo := endpointRepo.NewEndpoint(database)
	ctRepo := contactRepo.NewContact(database)
	ptRepo := prototypeRepo.NewPrototypeRepo(database)
	mtRepo := machineRepo.NewMachine(database)
	ptCache := cache.NewPrototypeCache()
	warrantyReturnSvc := returnsService.NewWarrantyReturn(repo, ueRepo, epRepo, ctRepo, ptRepo, mtRepo, ptCache)
	return &WarrantyReturnRouter{
		warrantyReturnHandler: warranty_return.NewWarrantyHandler(warrantyReturnSvc),
	}
}

func (w *WarrantyReturnRouter) Register(r *gin.RouterGroup) {
	g := r.Group("warranty_return")
	{
		g.POST("/return", w.warrantyReturnHandler.ReturnWarranty) // 保卡退货
	}
}
