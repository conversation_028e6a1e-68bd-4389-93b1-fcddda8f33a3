package dto

import "time"

// PromotionListItem 促销活动列表项
type PromotionListItem struct {
	Id                  int        `json:"id"`
	SalesPromotionId    int        `json:"sales_promotion_id"`
	Endpoint            int        `json:"endpoint"`
	ModelId             int        `json:"model_id"`
	Barcode             string     `json:"barcode"`
	BuyDate             *time.Time `json:"buy_date"`
	WarrantyId          int        `json:"warranty_id"`
	IsReceipt           int        `json:"is_receipt"`
	ReceiptAt           *time.Time `json:"receipt_at"`
	RegionName          string     `json:"region_name"`
	AgencyName          string     `json:"agency_name"`
	EndpointName        string     `json:"endpoint_name"`
	EndpointCode        string     `json:"endpoint_code"`
	Manager             string     `json:"manager"`
	EndpointPhone       string     `json:"endpoint_phone"`
	Address             string     `json:"address"`
}

// PromotionListDetail 促销活动详情
type PromotionListDetail struct {
	Id                  int        `json:"id"`
	SalesPromotionId    int        `json:"sales_promotion_id"`
	Endpoint            int        `json:"endpoint"`
	ModelId             int        `json:"model_id"`
	Barcode             string     `json:"barcode"`
	BuyDate             *time.Time `json:"buy_date"`
	WarrantyId          int        `json:"warranty_id"`
	IsReceipt           int        `json:"is_receipt"`
	ReceiptAt           *time.Time `json:"receipt_at"`
	StudentUid          string     `json:"student_uid"`
	StudentName         string     `json:"student_name"`
	AgencyName          string     `json:"agency_name"`
	ActivatedAt         string     `json:"activated_at"`
	Number              string     `json:"number"`
	HourInterval        int        `json:"hour_interval"`
	EndpointCode        string     `json:"endpoint_code"`
	EndpointName        string     `json:"endpoint_name"`
	Manager             string     `json:"manager"`
	ReturnAt            *time.Time `json:"return_at"`
}

// PromotionListReceipt 回执信息
type PromotionListReceipt struct {
	SalesPromotionListId int    `json:"sales_promotion_list_id"`
	Receipt              string `json:"receipt"`
	Number               string `json:"number"`
	Count                int    `json:"count"`
}

// WarrantyInfo 保修信息
type WarrantyInfo struct {
	Id              int    `json:"id"`
	Number          string `json:"number"`
	ActivatedAtOld  string `json:"activated_at_old"`
	StudentName     string `json:"student_name"`
}
