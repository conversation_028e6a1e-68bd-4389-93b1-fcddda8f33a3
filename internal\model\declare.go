package model

// DrpDeclareRecord 申报记录
type DrpDeclareRecord struct {
	ModelName  string `json:"model_name" gorm:"column:model_name"`   // 机型名称
	Time       int    `json:"time" gorm:"column:time"`               // 月份
	Inventory  int    `json:"inventory" gorm:"column:inventory"`     // 库存
	OldMachine int    `json:"old_machine" gorm:"column:old_machine"` // 新机总数
	Warehouse  int    `json:"warehouse" gorm:"column:warehouse"`     // 仓库id
	Declare    int    `json:"declare" gorm:"column:declare"`         // 是否允许申报 0:否 1:辞退
}

func (DrpDeclareRecord) TableName() string {
	return "drp_declare_record"
}

type DrpWarehouse struct {
	ID           int    `gorm:"column:id;primaryKey;autoIncrement"`
	Name         string `json:"warehouse" gorm:"column:warehouse"`         // 仓库名称
	TopAgency    int    `json:"top_agency" gorm:"column:top_agency"`       // 一代
	SecondAgency int    `json:"second_agency" gorm:"column:second_agency"` // 二代
	Endpoint     int    `json:"endpoint" gorm:"column:endpoint"`           // 终端
	Default      int    `json:"default" gorm:"column:default"`             // 是否默认 0:否 1:默认
	Type         string `json:"type" gorm:"column:type"`                   // 仓库类型
}

func (DrpWarehouse) TableName() string {
	return "drp_warehouse"
}
