package model

import (
	"gorm.io/gorm"
	"time"
)

// AdminUser 管理员用户模型
type AdminUser struct {
	ID            uint           `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	Username      string         `gorm:"type:varchar(190);uniqueIndex;not null;column:username" json:"username"`
	Password      string         `gorm:"type:varchar(60);not null;column:password" json:"-"`
	Name          string         `gorm:"type:varchar(255);not null;column:name" json:"name"`
	Avatar        *string        `gorm:"type:varchar(255);column:avatar" json:"avatar,omitempty"`
	Phone         *string        `gorm:"type:varchar(100);uniqueIndex;column:phone" json:"phone,omitempty"`
	VerifyCode    *int           `gorm:"column:code" json:"verify_code,omitempty"`
	QwUserID      *string        `gorm:"type:varchar(64);column:qw_userid" json:"qw_userid,omitempty"`
	ExpireTime    *time.Time     `gorm:"column:expire_time" json:"expire_time,omitempty"`
	RememberToken *string        `gorm:"type:varchar(100);column:remember_token" json:"remember_token,omitempty"`
	UserType      int8           `gorm:"type:tinyint(2);not null;default:-1;column:type" json:"user_type"` // 0:终端版, 1:管理版, 2:维修版
	Status        int8           `gorm:"type:tinyint(2);not null;default:1;column:status" json:"status"`   // 0:禁用, 1:正常
	CreatedAt     *time.Time     `gorm:"column:created_at" json:"created_at,omitempty"`
	UpdatedAt     *time.Time     `gorm:"column:updated_at" json:"updated_at,omitempty"`
	ActivedAt     *time.Time     `gorm:"column:actived_at" json:"actived_at,omitempty"`
	DeletedAt     gorm.DeletedAt `gorm:"index;column:deleted_at" json:"deleted_at,omitempty"`
	ResourceID    *int           `gorm:"column:resource_id" json:"resource_id,omitempty"`
}

// TableName 指定表名
func (AdminUser) TableName() string {
	return "admin_users"
}
