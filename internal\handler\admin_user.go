package handler

import (
	"marketing-app/internal/handler/dto"
	"marketing-app/internal/service"

	"github.com/gin-gonic/gin"
)

type AdminUserHandler interface {
	WecomLogin(c *gin.Context)
	PhoneLogin(c *gin.Context)
}
type adminUserHandler struct {
	adminUserService service.AdminUserService
}

func NewAdminUserHandler(adminUserService service.AdminUserService) AdminUserHandler {
	return &adminUserHandler{
		adminUserService: adminUserService,
	}
}

func (h *adminUserHandler) WecomLogin(c *gin.Context) {
	req := &dto.WecomLoginReq{}
	if err := c.ShouldBind<PERSON>(req); err != nil {
		ResponseError(c, err)
		return
	}

	userInfo, err := h.adminUserService.WecomLogin(c, req)
	if err != nil {
		ResponseError(c, err)
		return
	}

	ResponseSuccess(c, userInfo)
}

func (h *adminUserHandler) PhoneLogin(c *gin.Context) {
	req := &dto.PhoneLoginReq{}
	if err := c.ShouldBind<PERSON>(req); err != nil {
		ResponseError(c, err)
		return
	}

	userInfo, err := h.adminUserService.PhoneLogin(c, req)
	if err != nil {
		ResponseError(c, err)
		return
	}

	ResponseSuccess(c, userInfo)
}
