package districts

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	api "marketing-app/internal/api/client/districts"
	"marketing-app/internal/pkg/log"
)

type DistrictsService interface {
	FetchDistricts(ctx *gin.Context, pid string, level int) (map[string]interface{}, error)
}

type Districts struct {
	client api.DistrictClient
}

func NewDistrict(client api.DistrictClient) DistrictsService {
	return &Districts{
		client: client,
	}
}

// FetchDistricts 根据父ID和级别获取地区列表
func (s *Districts) FetchDistricts(ctx *gin.Context, pid string, level int) (map[string]interface{}, error) {
	districts, err := s.client.GetDistrictsData(ctx, pid, level)
	if err != nil {
		log.Error("Error fetching districts", zap.Error(err))
		return nil, err
	}
	return districts, nil
}
