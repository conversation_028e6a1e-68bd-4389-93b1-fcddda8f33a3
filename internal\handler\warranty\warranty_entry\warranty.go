package warranty_entry

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"marketing-app/internal/handler"
	"marketing-app/internal/handler/warranty/warranty_entry/dto"
	"marketing-app/internal/pkg/convertor/warranty_convertor"
	"marketing-app/internal/pkg/utils"
	"marketing-app/internal/router/warranty/warranty_entry/client"
	service "marketing-app/internal/service/warranty/warranty_entry"
	"time"
)

type WarrantyEntryHandler interface {
	CreateWarranty(c *gin.Context)
	CheckWarranty(c *gin.Context)
}

type Warranty struct {
	warrantySvc service.WarrantyEntryService
}

func NewWarrantyHandler(warrantySvc service.WarrantyEntryService) WarrantyEntryHandler {
	return &Warranty{
		warrantySvc: warrantySvc,
	}
}

func (w *Warranty) CreateWarranty(c *gin.Context) {
	var (
		req  client.CreateWarrantyRequest
		err  error
		resp dto.CreateWarrantyResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}
	// 测试环境下通过json直接传uid，发布模式下通过传token并且经过auth后从context中获取
	if gin.Mode() == gin.ReleaseMode {
		req.Uid = c.MustGet("uid").(int)
	}
	if err = utils.DateFormatValidate(time.DateOnly, req.StudentBirthday, req.BuyDate); err != nil {
		err = errors.Wrap(err, "date format error")
		return
	}
	r, err := w.warrantySvc.CreateWarranty(
		c,
		warranty_convertor.NewWarrantyEntryConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}
	resp = dto.CreateWarrantyResp{
		Message:        fmt.Sprintf("录入保卡成功!保卡id：%d", r.Id),
		CreateWarranty: *r,
	}
}

func (w *Warranty) CheckWarranty(c *gin.Context) {
	var (
		barcode string
		err     error
		machine map[string]interface{}
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, machine)
		}
	}()
	barcode = c.Param("barcode")
	if barcode == "" {
		err = errors.New("条码错误")
		return
	}
	// 验证条码格式
	if !utils.IsBarcode(barcode) {
		err = errors.New("条码错误")
		return
	}
	machine, err = w.warrantySvc.CheckWarranty(c, barcode)
	if err != nil {
		return
	}
}
