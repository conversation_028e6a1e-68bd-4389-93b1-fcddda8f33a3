package promotion_list

import (
	"errors"
	"fmt"
	"marketing-app/internal/handler/promotion/promotion_list/dto"
	repoDto "marketing-app/internal/repository/promotion/promotion_list/dto"
	"marketing-app/internal/repository/promotion/promotion_list"
	"marketing-app/internal/service/promotion/promotion_list/entity"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

type PromotionListService interface {
	GetList(c *gin.Context, param *entity.PromotionListsReq, isEndpoint bool) (*dto.PromotionListsResp, error)
	GetDetail(c *gin.Context, param *entity.PromotionListDetail, isEndpoint bool) (*dto.PromotionListDetailResp, error)
	UploadReceipt(c *gin.Context, param *entity.ReceiptUpload) error
}

type promotionListService struct {
	repo promotion_list.PromotionList
}

func NewPromotionListService(repo promotion_list.PromotionList) PromotionListService {
	return &promotionListService{
		repo: repo,
	}
}

func (s *promotionListService) GetList(c *gin.Context, param *entity.PromotionListsReq, isEndpoint bool) (*dto.PromotionListsResp, error) {
	// 获取列表数据
	items, total, err := s.repo.GetList(c, param, isEndpoint)
	if err != nil {
		return nil, err
	}

	// 转换为响应格式
	var result []dto.PromotionListItem
	var ids []int
	var warrantyIds []int

	for _, item := range items {
		ids = append(ids, item.Id)
		warrantyIds = append(warrantyIds, item.WarrantyId)
		
		resultItem := dto.PromotionListItem{
			Id:                item.Id,
			SalesPromotionId:  item.SalesPromotionId,
			Endpoint:          item.Endpoint,
			ModelId:           item.ModelId,
			Barcode:           item.Barcode,
			BuyDate:           item.BuyDate,
			WarrantyId:        item.WarrantyId,
			IsReceipt:         item.IsReceipt,
			ReceiptAt:         item.ReceiptAt,
			RegionName:        item.RegionName,
			AgencyName:        item.AgencyName,
			EndpointName:      item.EndpointName,
			EndpointCode:      item.EndpointCode,
			Manager:           item.Manager,
			EndpointPhone:     item.EndpointPhone,
			Address:           item.Address,
		}
		result = append(result, resultItem)
	}

	// 获取回执信息
	if len(ids) > 0 {
		receipts, err := s.repo.GetReceiptsByListIds(c, ids, isEndpoint)
		if err != nil {
			return nil, err
		}

		if isEndpoint {
			// 终端模式：获取保修信息
			warranties, err := s.repo.GetWarrantyInfoByIds(c, warrantyIds)
			if err != nil {
				return nil, err
			}

			// 创建保修信息映射
			warrantyMap := make(map[int]repoDto.WarrantyInfo)
			for _, w := range warranties {
				warrantyMap[w.Id] = w
			}

			// 处理回执和保修信息
			for i, item := range result {
				// 设置回执
				for _, receipt := range receipts {
					if item.Id == receipt.SalesPromotionListId {
						result[i].Receipt = receipt.Receipt
						break
					}
				}

				// 设置保修信息
				if warranty, exists := warrantyMap[item.WarrantyId]; exists {
					result[i].Number = warranty.Number
					result[i].ActivatedAt = warranty.ActivatedAtOld
					result[i].StudentName = warranty.StudentName
				}
			}
		} else {
			// 后台模式：处理回执列表
			receiptMap := make(map[int][]repoDto.PromotionListReceipt)
			for _, receipt := range receipts {
				receiptMap[receipt.SalesPromotionListId] = append(receiptMap[receipt.SalesPromotionListId], receipt)
			}

			for i, item := range result {
				if itemReceipts, exists := receiptMap[item.Id]; exists {
					// 按count排序
					promotion_list.SortReceiptsByCount(itemReceipts)
					result[i].Receipt = itemReceipts
				}
			}
		}
	}

	return &dto.PromotionListsResp{
		List:     result,
		Total:    total,
		Page:     param.Page,
		PageSize: param.PageSize,
	}, nil
}

func (s *promotionListService) GetDetail(c *gin.Context, param *entity.PromotionListDetail, isEndpoint bool) (*dto.PromotionListDetailResp, error) {
	// 获取详情数据
	detail, err := s.repo.GetDetail(c, param.Id, isEndpoint)
	if err != nil {
		return nil, err
	}

	if detail == nil {
		return nil, errors.New("记录不存在")
	}

	// 转换为响应格式
	result := &dto.PromotionListDetailResp{
		Id:               detail.Id,
		SalesPromotionId: detail.SalesPromotionId,
		Endpoint:         detail.Endpoint,
		ModelId:          detail.ModelId,
		Barcode:          detail.Barcode,
		BuyDate:          detail.BuyDate,
		WarrantyId:       detail.WarrantyId,
		IsReceipt:        detail.IsReceipt,
		ReceiptAt:        detail.ReceiptAt,
		StudentUid:       detail.StudentUid,
		StudentName:      detail.StudentName,
		AgencyName:       detail.AgencyName,
		ActivatedAt:      detail.ActivatedAt,
		Number:           detail.Number,
		HourInterval:     detail.HourInterval,
		EndpointCode:     detail.EndpointCode,
		EndpointName:     detail.EndpointName,
		Manager:          detail.Manager,
		ReturnAt:         detail.ReturnAt,
	}

	// 获取回执信息
	receipts, err := s.repo.GetReceiptsByListIds(c, []int{param.Id}, isEndpoint)
	if err != nil {
		return nil, err
	}

	if len(receipts) > 0 {
		if isEndpoint {
			result.Receipt = receipts[0].Receipt
		} else {
			promotion_list.SortReceiptsByCount(receipts)
			result.Receipt = receipts
		}
	}

	return result, nil
}

func (s *promotionListService) UploadReceipt(c *gin.Context, param *entity.ReceiptUpload) error {
	// 查询记录是否存在
	detail, err := s.repo.GetDetail(c, param.Id, false)
	if err != nil {
		return err
	}

	if detail == nil {
		return errors.New("记录不存在")
	}

	// 检查权限
	if detail.Endpoint != param.Endpoint {
		return fmt.Errorf("无权操作")
	}

	// 获取促销活动详情
	promotion, err := s.repo.GetPromotionDetail(c, detail.SalesPromotionId)
	if err != nil {
		return err
	}

	// 检查上传时间
	if promotion.ReceiptDay != nil {
		delayTime := promotion.ReceiptDay.Add(24 * time.Hour)
		if delayTime.Before(time.Now()) {
			return fmt.Errorf("回执上传已经过了截止时间")
		}
	}

	// 更新回执
	return s.repo.UpdateReceipt(c, param.Id, param.Endpoint, param.Receipt)
}
