package promotion_list

import (
	handler "marketing-app/internal/handler/promotion/promotion_list"
	"marketing-app/internal/pkg/db"
	repo "marketing-app/internal/repository/promotion/promotion_list"
	svc "marketing-app/internal/service/promotion/promotion_list"

	"github.com/gin-gonic/gin"
)

type PromotionListRouter struct {
	promotionListHandler handler.PromotionList
}

func NewPromotionListRouter() *PromotionListRouter {
	database, _ := db.GetDB()
	promotionListRepo := repo.NewPromotionList(database)
	promotionListSvc := svc.NewPromotionListService(promotionListRepo)
	return &PromotionListRouter{
		promotionListHandler: handler.NewPromotionListHandler(promotionListSvc),
	}
}

func (p *PromotionListRouter) Register(r *gin.RouterGroup) {
	g := r.Group("/app/promotion")
	{
		// 名单列表
		g.GET("/list", p.promotionListHandler.GetList)
		// 名单详情
		g.GET("/list/:id", p.promotionListHandler.GetDetail)
		// 上传回执
		g.POST("/list/:id", p.promotionListHandler.UploadReceipt)
	}
}
