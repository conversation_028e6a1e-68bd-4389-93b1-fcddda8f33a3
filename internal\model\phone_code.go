package model

import "time"

type PhoneCode struct {
	ID        uint      `gorm:"primaryKey;autoIncrement;column:id"`
	Phone     string    `gorm:"unique;size:50;not null;default:'';column:phone"`
	Code      string    `gorm:"size:50;not null;default:'';column:code"`
	UpdatedAt time.Time `gorm:"not null;default:CURRENT_TIMESTAMP;column:updated_at"`
	Consume   int       `gorm:"not null;default:0;column:consume"`
}

// TableName overrides the table name used by GORM.
func (PhoneCode) TableName() string {
	return "phone_code"
}
