package service

import (
	"errors"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"go.uber.org/zap"
	"marketing-app/internal/consts"
	"marketing-app/internal/handler/dto"
	"marketing-app/internal/pkg/log"
	"marketing-app/internal/pkg/wecom"
	"marketing-app/internal/repository"
	"time"
)

type AdminUserService interface {
	WecomLogin(c *gin.Context, req *dto.WecomLoginReq) (*dto.UserInfoResp, error)
	PhoneLogin(c *gin.Context, req *dto.PhoneLoginReq) (*dto.UserInfoResp, error)
	ValidateToken(tokenString, jwtKey string) (*Claims, error)
}

type adminUser struct {
	adminUserRepo    repository.AdminUserRepository
	appSystemService AppSystemService
	phoneCodeRepo    repository.PhoneCodeRepository
}

func NewAdminUserService(adminUserRepo repository.AdminUserRepository,
	appSystemService AppSystemService,
	phoneCodeRepo repository.PhoneCodeRepository) AdminUserService {
	return &adminUser{
		adminUserRepo:    adminUserRepo,
		appSystemService: appSystemService,
		phoneCodeRepo:    phoneCodeRepo,
	}
}

type Claims struct {
	UserID     uint   `json:"user_id"`
	UserName   string `json:"user_name"`
	Name       string `json:"name"`
	SystemType string `json:"system_type"`
	jwt.RegisteredClaims
}

func (s *adminUser) WecomLogin(c *gin.Context, req *dto.WecomLoginReq) (*dto.UserInfoResp, error) {
	// 查询系统是否存在
	systemInfo, err := s.appSystemService.GetAppSystem(c)
	if err != nil {
		return nil, err
	}
	systemType := systemInfo.AppKey

	// 获取企微客户端
	var weComClient *wecom.Client
	weComClient = wecom.NewWeComClient(systemInfo.CorpID, systemInfo.CorpSecret)

	//2、根据用户名判断用户是否可用
	qwUserID, err := weComClient.GetUserIDByCode(req.Code)
	if err != nil {
		return nil, err
	}

	user, err := s.adminUserRepo.GetByQwUserID(c, qwUserID)
	if err != nil {
		return nil, err
	}

	if user == nil {
		return nil, errors.New("用户不存在")
	}

	if user.Status != 1 {
		return nil, errors.New("用户已经被禁用")
	}

	// 生成token
	token, err := s.generateToken(c, user.ID, user.Username, user.Name, systemType, systemInfo.JwtKey, consts.AccessTokenExpiresIn)
	if err != nil {
		return nil, err
	}
	refreshToken, err := s.generateToken(c, user.ID, user.Username, user.Name, systemType, systemInfo.JwtKey, consts.RefreshTokenExpiresIn)

	resp := &dto.UserInfoResp{
		ID:           user.ID,
		Avatar:       *user.Avatar,
		Username:     user.Username,
		Phone:        *user.Phone,
		Token:        token,
		RefreshToken: refreshToken,
		Name:         user.Name,
	}
	// 更新用户活跃时间
	go func() {
		err = s.adminUserRepo.UpdateUserActiveTime(c, user.ID)
		if err != nil {
			log.Error("更新用户活跃时间失败", zap.Error(err), zap.Uint("userID", user.ID))
		}
	}()
	return resp, nil
}

func (s *adminUser) PhoneLogin(c *gin.Context, req *dto.PhoneLoginReq) (*dto.UserInfoResp, error) {
	// 查询系统是否存在
	systemInfo, err := s.appSystemService.GetAppSystem(c)
	if err != nil {
		return nil, err
	}
	systemType := systemInfo.AppKey

	user, err := s.adminUserRepo.GetAdminUserByPhone(c, req.Phone)
	if err != nil {
		return nil, err
	}

	if user == nil {
		return nil, errors.New("用户不存在")
	}

	if user.Status != 1 {
		return nil, errors.New("用户已经被禁用")
	}

	//判断验证码是否有效
	phoneCode, err := s.phoneCodeRepo.GetPhoneCode(c, req.Phone, req.Code)
	if err != nil || phoneCode == nil {
		return nil, errors.New("验证码错误")
	}
	expiresAt := phoneCode.UpdatedAt.Add(consts.PhoneCodeExpiresIn)
	if time.Now().After(expiresAt) {
		return nil, errors.New("验证码已过期")
	}

	if phoneCode.Consume == 1 {
		return nil, errors.New("验证码已失效")
	}

	// 生成token
	token, err := s.generateToken(c, user.ID, user.Username, user.Name, systemType, systemInfo.JwtKey, consts.AccessTokenExpiresIn)
	if err != nil {
		return nil, err
	}
	refreshToken, err := s.generateToken(c, user.ID, user.Username, user.Name, systemType, systemInfo.JwtKey, consts.RefreshTokenExpiresIn)

	resp := &dto.UserInfoResp{
		ID:           user.ID,
		Avatar:       *user.Avatar,
		Username:     user.Username,
		Phone:        *user.Phone,
		Token:        token,
		RefreshToken: refreshToken,
		Name:         user.Name,
	}
	// 更新用户活跃时间
	go func() {
		err = s.adminUserRepo.UpdateUserActiveTime(c, user.ID)
		if err != nil {
			log.Error("更新用户活跃时间失败", zap.Error(err), zap.Uint("userID", user.ID))
		}
	}()
	return resp, nil
}

func (s *adminUser) ValidateToken(tokenString, jwtKey string) (*Claims, error) {
	claims := &Claims{}
	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		return []byte(jwtKey), nil
	})

	if err != nil || !token.Valid {
		return nil, err
	}
	return claims, nil
}

func (s *adminUser) generateToken(c *gin.Context, uid uint, username, name, systemType, jwtKey string, expiresIn time.Duration) (string, error) {
	expirationTime := time.Now().Add(expiresIn)
	claims := &Claims{
		UserID:     uid,
		UserName:   username,
		Name:       name,
		SystemType: systemType,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
		},
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(jwtKey))
}
