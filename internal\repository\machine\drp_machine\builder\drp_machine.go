package builder

import (
	"gorm.io/gorm"
	"marketing-app/internal/repository/machine/drp_machine/dto"
)

type DrpMachine struct {
	Barcode       string        `json:"barcode"`
	NewBarcode    string        `json:"new_barcode"`
	OperationType int           `json:"operation_type"`
	StatusNotIn   []int         `json:"status"`
	Return        int           `json:"return"`
	Warranty      *dto.Warranty `json:"return_warranty"`
}

func NewDrpMachine() *DrpMachine {
	return &DrpMachine{
		Warranty: nil,
	}
}

func (d *DrpMachine) Fill(db *gorm.DB) *gorm.DB {
	db = d.fillWhere(db)
	return db
}

func (d *DrpMachine) fillWhere(db *gorm.DB) *gorm.DB {
	if d.Barcode != "" {
		db.Where("barcode = ?", d.Barcode)
	}
	if len(d.StatusNotIn) > 0 {
		db.Where("status NOT IN (?)", d.StatusOmit)
	}
	return db
}

func (d *DrpMachine) BarcodeEq(v string) *DrpMachine {
	d.Barcode = v
	return d
}

func (d *DrpMachine) StatusOmit(v ...int) *DrpMachine {
	d.StatusNotIn = v
	return d
}

func (d *DrpMachine) OperationTypeEq(v int) *DrpMachine {
	d.OperationType = v
	return d
}

func (d *DrpMachine) ReturnEq(v int) *DrpMachine {
	d.Return = v
	return d
}

func (d *DrpMachine) WarrantyEq(v *dto.Warranty) *DrpMachine {
	d.Warranty = v
	return d
}

func (d *DrpMachine) NewBarcodeEq(v string) *DrpMachine {
	d.NewBarcode = v
	return d
}
