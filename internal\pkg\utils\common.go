package utils

import (
	"encoding/json"
	"strings"
)

// ToJSON 将数据转换为JSON字符串
func ToJSON(v interface{}) string {
	if v == nil {
		return ""
	}

	// 使用 json.MarshalIndent 生成格式化的 JSON
	bytes, err := json.Marshal(v)
	if err != nil {
		return ""
	}

	return string(bytes)
}

// JSONMaskFields 对JSON中的敏感字段进行脱敏
func JSONMaskFields(jsonStr string, sensitiveFields []string) string {
	if jsonStr == "" || len(sensitiveFields) == 0 {
		return jsonStr
	}

	var data map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return jsonStr
	}

	// 递归处理敏感字段
	data = MaskMap(data, sensitiveFields)

	// 重新序列化
	//result, err := json.MarshalIndent(data, "", "    ")
	result, err := json.<PERSON>(data)
	if err != nil {
		return jsonStr
	}

	return string(result)
}

// MaskMap 递归处理 map 中的敏感字段
func MaskMap(data map[string]interface{}, sensitiveFields []string) map[string]interface{} {
	for k, v := range data {
		switch val := v.(type) {
		case map[string]interface{}:
			MaskMap(val, sensitiveFields)
		case []interface{}:
			for _, item := range val {
				if m, ok := item.(map[string]interface{}); ok {
					MaskMap(m, sensitiveFields)
				}
			}
		default:
			// 检查是否是敏感字段
			for _, field := range sensitiveFields {
				if strings.EqualFold(k, field) {
					data[k] = "******"
					break
				}
			}
		}
	}
	return data
}
