package builder

import "gorm.io/gorm"

type MachineType struct {
	ModelId         int  `json:"model_id"`
	Visibility      *int `json:"visibility"`
	PrototypeStatus int  `json:"prototype_status"`
}

func NewMachineType() *MachineType {
	return &MachineType{Visibility: nil}
}

func (m *MachineType) Fill(db *gorm.DB) *gorm.DB {
	db = m.fillWhere(db)
	return db
}

func (m *MachineType) fillWhere(db *gorm.DB) *gorm.DB {
	if m.ModelId != 0 {
		db = db.Where("model_id = ?", m.ModelId)
	}
	if m.Visibility != nil {
		db = db.Where("visibility = ?", *m.Visibility)
	}
	if m.PrototypeStatus != 0 {
		db = db.Where("prototype_status = ?", m.PrototypeStatus)
	}
	return db
}

func (m *MachineType) ModelIdEq(v int) *MachineType {
	m.ModelId = v
	return m
}

func (m *MachineType) PrototypeStatusEq(v int) *MachineType {
	m.PrototypeStatus = v
	return m
}

func (m *MachineType) VisibilityEq(v int) *MachineType {
	m.Visibility = &v
	return m
}
