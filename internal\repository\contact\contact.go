package contact

import (
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"marketing-app/internal/model"
	"marketing-app/internal/pkg/log"
	"marketing-app/internal/repository/contact/builder"
	"time"
)

type Contact interface {
	Load(ctx *gin.Context, query *builder.Contact) error
	Check(ctx *gin.Context, tx *gorm.DB, query *builder.Contact) (id uint64, err error)
	Update(ctx *gin.Context, cond *builder.Contact, upd map[string]interface{}) (err error)
	CreateContactMachine(ctx *gin.Context, tx *gorm.DB, create *model.ContactMachine) (id uint, err error)
	CheckContactMachine(ctx *gin.Context, tx *gorm.DB, query *builder.ContactMachine) (data *model.ContactMachine, err error)
}

type contact struct {
	db *gorm.DB
}

func NewContact(db *gorm.DB) Contact {
	return &contact{
		db: db,
	}
}

func (c *contact) UseTransaction(tx *gorm.DB) *gorm.DB {
	if tx == nil {
		return c.db
	}
	return tx
}

// Load 从保卡数据中导入联系人
func (c *contact) Load(ctx *gin.Context, query *builder.Contact) error {
	return c.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		w := query.Warranty
		var err error
		var addContact *model.Contact
		var updateContact *model.Contact

		contactID, err := c.Check(
			ctx,
			tx,
			builder.NewContact().EndpointIdEq(w.EndpointID).CustomerPhoneEq(w.CustomerPhone),
		)
		now := time.Now()

		if contactID == 0 {
			// 创建联系人
			addContact = &model.Contact{
				EndpointID:      uint(w.EndpointID),
				CustomerName:    w.CustomerName,
				CustomerPhone:   w.CustomerPhone,
				StudentName:     w.StudentName,
				StudentSchool:   w.StudentSchool,
				StudentGrade:    w.StudentGrade,
				StudentBirthday: w.StudentBirthday,
				Salesman:        w.Salesman,
				Manager:         uint(w.SalesmanID),
				CreatedAt:       now,
				UpdatedAt:       &now,
				CustomerSex:     w.CustomerSex,
				StudentSex:      w.StudentSex,
				Type:            "purchased",
				SubType:         "satisfaction_unknown",
				LastPurchasedAt: w.BuyDate,
			}
			err = tx.WithContext(ctx).Model(&model.Contact{}).Create(&addContact).Error
			if err != nil {
				return err
			}
			contactID = addContact.ID
		} else {
			// 更新联系人
			updateContact = &model.Contact{
				Type:            "purchased",
				SubType:         "satisfaction_unknown",
				Manager:         uint(w.SalesmanID),
				CustomerName:    w.CustomerName,
				CustomerPhone:   w.CustomerPhone,
				StudentName:     w.StudentName,
				StudentSchool:   w.StudentSchool,
				StudentGrade:    w.StudentGrade,
				StudentBirthday: w.StudentBirthday,
				Salesman:        w.Salesman,
				LastPurchasedAt: w.BuyDate,
				CustomerSex:     w.CustomerSex,
				StudentSex:      w.StudentSex,
				UpdatedAt:       &now,
			}
			if err = tx.Model(&model.Contact{}).Where("id = ?", contactID).Updates(updateContact).Error; err != nil {
				err = errors.Wrap(err, "updating contact err")
				return err
			}
		}

		if w.Model != "" {
			contactMachine, err := c.CheckContactMachine(
				ctx,
				tx,
				builder.NewContactMachine().EndpointIdEq(w.EndpointID).NameEq(w.Model),
			)
			if err != nil {
				return err
			}
			if contactMachine.ID == 0 {
				_, err = c.CreateContactMachine(ctx, tx, &model.ContactMachine{
					EndpointID: w.EndpointID,
					Name:       w.Model,
				})
				if err != nil {
					return err
				}
			}
			// 创建联系人与机型的关联
			var relation *model.ContactMachineRelation
			relation = &model.ContactMachineRelation{
				EndpointID: w.EndpointID,
				ContactID:  int(contactID),
				CmID:       int(contactMachine.ID),
			}
			if err = tx.WithContext(ctx).FirstOrCreate(&relation, relation).Error; err != nil {
				err = errors.Wrap(err, "creating contact machine relation err")
				return err
			}

			if contactMachine.ID > 0 {

			}
		}

		return nil
	})
}

func (c *contact) CreateContactMachine(ctx *gin.Context, tx *gorm.DB, create *model.ContactMachine) (id uint, err error) {
	err = c.UseTransaction(tx).WithContext(ctx).Model(&model.ContactMachine{}).Create(create).Error
	if err != nil {
		err = errors.Wrap(err, "adding machine err")
		return
	}
	return create.ID, nil
}

func (c *contact) CheckContactMachine(ctx *gin.Context, tx *gorm.DB, query *builder.ContactMachine) (data *model.ContactMachine, err error) {
	err = query.Fill(c.UseTransaction(tx).WithContext(ctx).Model(&model.ContactMachine{})).Find(&data).Error
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (c *contact) Check(ctx *gin.Context, tx *gorm.DB, query *builder.Contact) (id uint64, err error) {
	var data model.Contact
	err = query.Fill(c.UseTransaction(tx).WithContext(ctx).Model(&model.Contact{})).First(&data).Error
	if err != nil {
		log.Error("contact record not found", zap.Error(err))
		return
	}
	return data.ID, nil
}

func (c *contact) Update(ctx *gin.Context, cond *builder.Contact, upd map[string]interface{}) (err error) {
	return cond.Fill(c.db.WithContext(ctx).Model(&model.Contact{})).Updates(upd).Error
}
