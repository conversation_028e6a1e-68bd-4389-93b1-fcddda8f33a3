package service

import (
	"encoding/json"
	"fmt"
	"marketing-app/internal/handler/dto"
	"marketing-app/internal/pkg/log"
	"marketing-app/internal/pkg/utils"
	"marketing-app/internal/repository"
	"regexp"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

type NotificationService interface {
	// GetUserMessages 获取用户通知消息
	GetUserMessages(c *gin.Context, userID uint, req *dto.NotificationMessagesReq) ([]dto.NotificationMessage, error)
	// GetUserMessageDetail 获取用户单条消息详情
	GetUserMessageDetail(c *gin.Context, userID uint, messageID uint) (*dto.NotificationDetailResp, error)
	// UpdateNotificationRead 更新通知已读状态
	UpdateNotificationRead(c *gin.Context, userID uint, req *dto.NotificationStatusUpdateReq) error
	// UpdateNotificationChecked 更新通知已查看状态
	UpdateNotificationChecked(c *gin.Context, userID uint, req *dto.NotificationStatusUpdateReq) error
}

type notificationService struct {
	notificationRepo repository.NotificationRepository
}

func NewNotificationService(notificationRepo repository.NotificationRepository) NotificationService {
	return &notificationService{
		notificationRepo: notificationRepo,
	}
}

func (s *notificationService) GetUserMessages(c *gin.Context, userID uint, req *dto.NotificationMessagesReq) ([]dto.NotificationMessage, error) {
	// 设置默认页面大小
	pageSize := req.PageSize
	if pageSize == 0 {
		pageSize = 100
	}

	// 从数据库获取通知消息
	messages, err := s.notificationRepo.GetUserNotifications(c, userID, req.Platform, pageSize, req.Page)
	if err != nil {
		return nil, err
	}

	// 转换为响应格式
	var result []dto.NotificationMessage
	var ids []uint

	for _, msg := range messages {
		// 解析content JSON
		var content map[string]interface{}
		if err := json.Unmarshal(msg.Content, &content); err != nil {
			// 如果解析失败，跳过这条消息
			continue
		}

		// 添加额外字段到content中
		content["id"] = msg.ID
		content["created_at"] = msg.CreatedAt
		content["slug"] = msg.Slug
		content["read"] = msg.Read
		content["checked"] = msg.Checked
		content["group_icon"] = utils.AddPrefix(content["group_icon"].(string))
		notificationMsg := dto.NotificationMessage{
			ID:        msg.ID,
			CreatedAt: msg.CreatedAt,
			Slug:      msg.Slug,
			Read:      msg.Read,
			Checked:   msg.Checked,
			Content:   content,
		}

		result = append(result, notificationMsg)
		ids = append(ids, msg.ID)
	}

	// 如果有消息，更新拉取状态
	if len(ids) > 0 {
		err = s.notificationRepo.UpdateInboxFetched(c, ids)
		if err != nil {
			// 记录错误但不影响返回结果
			// 可以在这里添加日志记录
			log.Error("更新消息拉取记录失败")
		}
	}

	return result, nil
}

// GetUserMessageDetail 获取用户单条消息详情
func (s *notificationService) GetUserMessageDetail(c *gin.Context, userID uint, messageID uint) (*dto.NotificationDetailResp, error) {
	// 从数据库获取消息详情
	message, err := s.notificationRepo.GetUserNotificationDetail(c, userID, messageID)
	if err != nil {
		return nil, err
	}

	// 解析content JSON
	var content map[string]interface{}
	if err := json.Unmarshal(message.Content, &content); err != nil {
		return nil, fmt.Errorf("failed to parse message content: %w", err)
	}

	// 添加额外字段到content中
	content["id"] = message.ID
	content["created_at"] = message.CreatedAt
	content["slug"] = message.Slug
	content["read"] = message.Read
	content["checked"] = message.Checked
	content["group_icon"] = utils.AddPrefix(content["group_icon"].(string))

	// 构建响应
	result := &dto.NotificationDetailResp{
		ID:        message.ID,
		CreatedAt: message.CreatedAt,
		Slug:      message.Slug,
		Read:      message.Read,
		Checked:   message.Checked,
		Content:   content,
	}

	// 更新拉取状态
	err = s.notificationRepo.UpdateInboxFetched(c, []uint{message.ID})
	if err != nil {
		// 记录错误但不影响返回结果
		log.Error("更新消息拉取记录失败")
	}
	//拉取了详情代表已查看
	err = s.notificationRepo.UpdateNotificationStatus(c, userID, []uint{message.ID}, "checked")
	if err != nil {
		log.Error("更新消息查看状态失败")
	}

	return result, nil
}

// UpdateNotificationRead 更新通知已读状态
func (s *notificationService) UpdateNotificationRead(c *gin.Context, userID uint, req *dto.NotificationStatusUpdateReq) error {
	// 解析ID列表
	ids, err := s.parseIDList(req.IDList)
	if err != nil {
		return err
	}

	// 调用repository更新read状态
	return s.notificationRepo.UpdateNotificationStatus(c, userID, ids, "read")
}

// UpdateNotificationChecked 更新通知已查看状态
func (s *notificationService) UpdateNotificationChecked(c *gin.Context, userID uint, req *dto.NotificationStatusUpdateReq) error {
	// 解析ID列表
	ids, err := s.parseIDList(req.IDList)
	if err != nil {
		return err
	}

	// 调用repository更新checked状态
	return s.notificationRepo.UpdateNotificationStatus(c, userID, ids, "checked")
}

// parseIDList 解析ID列表字符串为uint切片
func (s *notificationService) parseIDList(idList string) ([]uint, error) {
	// 验证ID列表格式
	matched, err := regexp.MatchString(`^\d+(,\d+)*$`, idList)
	if err != nil {
		return nil, err
	}
	if !matched {
		return nil, fmt.Errorf("invalid id_list format: %s", idList)
	}

	idStrings := strings.Split(idList, ",")
	ids := make([]uint, 0, len(idStrings))

	for _, idStr := range idStrings {
		id, err := strconv.ParseUint(strings.TrimSpace(idStr), 10, 32)
		if err != nil {
			return nil, err
		}
		ids = append(ids, uint(id))
	}

	return ids, nil
}
