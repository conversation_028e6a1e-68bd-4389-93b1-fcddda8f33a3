package service

import (
	"errors"
	"fmt"
	"marketing-app/internal/handler/dto"
	"marketing-app/internal/repository"
	"sort"
	"time"

	"github.com/gin-gonic/gin"
)

type PromotionListService interface {
	GetList(c *gin.Context, param *dto.PromotionListsReq, isEndpoint bool) (*dto.PromotionListsResp, error)
	GetDetail(c *gin.Context, param *dto.PromotionListDetailReq, isEndpoint bool) (*dto.PromotionListDetailResp, error)
	UploadReceipt(c *gin.Context, param *dto.ReceiptUploadReq, endpoint int) error
}

type promotionListService struct {
	repo repository.PromotionListRepository
}

func NewPromotionListService(repo repository.PromotionListRepository) PromotionListService {
	return &promotionListService{
		repo: repo,
	}
}

func (s *promotionListService) GetList(c *gin.Context, param *dto.PromotionListsReq, isEndpoint bool) (*dto.PromotionListsResp, error) {
	// 获取列表数据
	items, total, err := s.repo.GetList(c, param, isEndpoint)
	if err != nil {
		return nil, err
	}

	// 收集ID用于查询关联数据
	var ids []int
	var warrantyIds []int
	for _, item := range items {
		ids = append(ids, item.Id)
		warrantyIds = append(warrantyIds, item.WarrantyId)
	}

	// 获取回执信息
	if len(ids) > 0 {
		receipts, err := s.repo.GetReceiptsByListIds(c, ids, isEndpoint)
		if err != nil {
			return nil, err
		}

		if isEndpoint {
			// 终端模式：获取保修信息
			warranties, err := s.repo.GetWarrantyInfoByIds(c, warrantyIds)
			if err != nil {
				return nil, err
			}

			// 处理回执和保修信息
			for i, item := range items {
				// 设置回执
				for _, receipt := range receipts {
					if item.Id == receipt.SalesPromotionListId {
						items[i].Receipt = receipt.Receipt
						break
					}
				}

				// 设置保修信息
				if warranty, exists := warranties[item.WarrantyId]; exists {
					items[i].Number = warranty.Number
					items[i].ActivatedAt = warranty.ActivatedAtOld
					items[i].StudentName = warranty.StudentName
				}
			}
		} else {
			// 后台模式：处理回执列表
			receiptMap := make(map[int][]dto.PromotionListReceiptResp)
			for _, receipt := range receipts {
				receiptMap[receipt.SalesPromotionListId] = append(receiptMap[receipt.SalesPromotionListId], receipt)
			}

			for i, item := range items {
				if itemReceipts, exists := receiptMap[item.Id]; exists {
					// 按count排序
					sort.Slice(itemReceipts, func(i, j int) bool {
						return itemReceipts[i].Count > itemReceipts[j].Count
					})
					items[i].Receipt = itemReceipts
				}
			}
		}
	}

	return &dto.PromotionListsResp{
		List:     items,
		Total:    total,
		Page:     param.Page,
		PageSize: param.PageSize,
	}, nil
}

func (s *promotionListService) GetDetail(c *gin.Context, param *dto.PromotionListDetailReq, isEndpoint bool) (*dto.PromotionListDetailResp, error) {
	// 获取详情数据
	detail, err := s.repo.GetDetail(c, param.Id, isEndpoint)
	if err != nil {
		return nil, err
	}

	if detail == nil {
		return nil, errors.New("记录不存在")
	}

	// 获取回执信息
	receipts, err := s.repo.GetReceiptsByListIds(c, []int{param.Id}, isEndpoint)
	if err != nil {
		return nil, err
	}

	if len(receipts) > 0 {
		if isEndpoint {
			detail.Receipt = receipts[0].Receipt
		} else {
			// 按count排序
			sort.Slice(receipts, func(i, j int) bool {
				return receipts[i].Count > receipts[j].Count
			})
			detail.Receipt = receipts
		}
	}

	return detail, nil
}

func (s *promotionListService) UploadReceipt(c *gin.Context, param *dto.ReceiptUploadReq, endpoint int) error {
	// 查询记录是否存在
	detail, err := s.repo.GetDetail(c, param.Id, false)
	if err != nil {
		return err
	}

	if detail == nil {
		return errors.New("记录不存在")
	}

	// 检查权限
	if detail.Endpoint != endpoint {
		return fmt.Errorf("无权操作")
	}

	// 获取促销活动详情
	promotion, err := s.repo.GetPromotionDetail(c, detail.SalesPromotionId)
	if err != nil {
		return err
	}

	// 检查上传时间
	if promotion.ReceiptDay != nil {
		delayTime := promotion.ReceiptDay.Add(24 * time.Hour)
		if delayTime.Before(time.Now()) {
			return fmt.Errorf("回执上传已经过了截止时间")
		}
	}

	// 更新回执
	return s.repo.UpdateReceipt(c, param.Id, endpoint, param.Receipt)
}
