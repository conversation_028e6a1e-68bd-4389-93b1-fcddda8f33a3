package warranty_homepage

import (
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"marketing-app/internal/consts"
	"marketing-app/internal/pkg/utils"
	userEndpointRepo "marketing-app/internal/repository/endpoint/user_endpoint"
	baseRepo "marketing-app/internal/repository/warranty/base"
	"marketing-app/internal/repository/warranty/base/builder"
	"marketing-app/internal/repository/warranty/base/dto"
	"marketing-app/internal/service/warranty/warranty_homepage/entity"
)

type WarrantyBaseService interface {
	GetList(c *gin.Context, params *entity.Warranty) ([]dto.WarrantyBase, int64, error)
	GetByIdentifier(c *gin.Context, param *entity.Identifier) ([]dto.WarrantyDetail, int64, error)
}

type WarrantyBase struct {
	repo             baseRepo.Warranty
	userEndpointRepo userEndpointRepo.UserEndpoint
}

func NewWarrantyBase(repo baseRepo.Warranty, userEndpointRepo userEndpointRepo.UserEndpoint) WarrantyBaseService {
	return &WarrantyBase{
		repo:             repo,
		userEndpointRepo: userEndpointRepo,
	}
}

func (w *WarrantyBase) GetList(c *gin.Context, params *entity.Warranty) ([]dto.WarrantyBase, int64, error) {
	data, total, err := w.repo.GetList(
		c,
		builder.NewWarranty().
			JoinUserEndpoint("RIGHT JOIN user_endpoint ON warranty.endpoint = user_endpoint.endpoint").
			UidEq(params.Uid).
			ModelEq(params.Model).
			SalesmanIdEq(params.SalesmanId).
			StatusIn(consts.WarrantyStatusActive).
			DoOrderBy("id desc").
			DateRange(&builder.PurchaseDateRange{
				StartDate: params.BuyDateStart,
				EndDate:   params.BuyDateEnd,
			}).
			Pagination(&builder.PaginationParm{
				Page:     params.Page,
				PageSize: params.PageSize,
			}),
	)
	return data, total, err
}

func (w *WarrantyBase) GetByIdentifier(c *gin.Context, param *entity.Identifier) (data []dto.WarrantyDetail, total int64, err error) {
	endpoint, err := w.userEndpointRepo.GetEndpointAgencyByUid(c, param.Uid)
	if endpoint == nil {
		err = errors.New("账号无权限查询电子保卡")
		return
	}
	data, total, err = w.findData(c, &entity.IdentifierEndpointId{
		Identifier: *param,
		EndpointId: endpoint.ID,
	})
	if err != nil {
		err = errors.Wrap(err, "编码没有电子保卡信息")
	}
	return
}

func (w *WarrantyBase) GetByBarcode(c *gin.Context, barcode string) (data []dto.WarrantyDetail, total int64, err error) {
	data, total, err = w.repo.GetDetails(
		c,
		builder.NewWarranty().
			JoinEndpoint("LEFT JOIN endpoint ON warranty.endpoint = endpoint.id").
			BarcodeOrExtBarcodeEq(barcode, barcode).
			StatusIn(consts.WarrantyStatusActive),
	)
	return
}

func (w *WarrantyBase) GetByPhone(c *gin.Context, endpointId int, phone string) (data []dto.WarrantyDetail, total int64, err error) {
	data, total, err = w.repo.GetDetails(
		c,
		builder.NewWarranty().
			JoinEndpoint("LEFT JOIN endpoint ON warranty.endpoint = endpoint.id").
			EndpointEq(endpointId).
			CustomerPhoneEq(phone).
			StatusIn(consts.WarrantyStatusActive),
	)
	return
}

func (w *WarrantyBase) GetByNumber(c *gin.Context, number string) (data []dto.WarrantyDetail, total int64, err error) {
	data, total, err = w.repo.GetDetails(
		c,
		builder.NewWarranty().
			JoinEndpoint("LEFT JOIN endpoint ON warranty.endpoint = endpoint.id").
			NumberEq(number).
			StatusIn(consts.WarrantyStatusActive),
	)
	return
}

func (w *WarrantyBase) GetByImei(c *gin.Context, imei string) (data []dto.WarrantyDetail, total int64, err error) {
	m, err := utils.CheckMachine(utils.CheckMachineParams{
		Imei: imei,
	})
	if err != nil {
		return
	}
	barcode := m["barcode"].(string)
	data, total, err = w.GetByBarcode(c, barcode)
	return
}

func (w *WarrantyBase) findData(c *gin.Context, param *entity.IdentifierEndpointId) ([]dto.WarrantyDetail, int64, error) {
	barcodeSearcher := &BarcodeSearcher{warranty: w}
	imeiSearcher := &ImeiSearcher{warranty: w}
	phoneSearcher := &PhoneSearcher{warranty: w}
	numberSearcher := &NumberSearcher{warranty: w}

	chain := barcodeSearcher
	chain.SetNext(phoneSearcher).SetNext(numberSearcher).SetNext(imeiSearcher)

	request := &SearchRequest{
		Context:    c,
		Query:      param.Query,
		EndpointId: param.EndpointId,
	}

	return chain.Find(request)
}
