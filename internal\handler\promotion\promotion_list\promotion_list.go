package promotion_list

import (
	"marketing-app/internal/handler"
	"marketing-app/internal/handler/promotion/promotion_list/dto"
	"marketing-app/internal/pkg/convertor/promotion_convertor"
	"marketing-app/internal/router/promotion/promotion_list/client"
	service "marketing-app/internal/service/promotion/promotion_list"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

type PromotionList interface {
	GetList(c *gin.Context)
	GetDetail(c *gin.Context)
	UploadReceipt(c *gin.Context)
}

type promotionList struct {
	promotionListSvc service.PromotionListService
}

func NewPromotionListHandler(svc service.PromotionListService) PromotionList {
	return &promotionList{
		promotionListSvc: svc,
	}
}

// GetList 获取促销活动列表
func (p *promotionList) GetList(c *gin.Context) {
	var (
		req  client.PromotionListsRequest
		err  error
		resp *dto.PromotionListsResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定请求参数
	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}

	// 获取用户信息 - 这里需要根据实际的认证中间件来获取
	// 假设通过中间件设置了endpoint信息
	var isEndpoint bool
	var endpoint int
	
	// 检查是否是终端用户
	if endpointVal, exists := c.Get("endpoint"); exists {
		if endpointInfo, ok := endpointVal.(map[string]interface{}); ok {
			if id, ok := endpointInfo["id"].(int); ok {
				endpoint = id
				isEndpoint = true
				req.Endpoint = endpoint // 终端用户只能查看自己的数据
			}
		}
	}

	// 调用服务
	resp, err = p.promotionListSvc.GetList(
		c,
		promotion_convertor.NewPromotionListConvertor().ClientToEntity(&req),
		isEndpoint,
	)
}

// GetDetail 获取促销活动详情
func (p *promotionList) GetDetail(c *gin.Context) {
	var (
		req  client.PromotionListDetailRequest
		err  error
		resp *dto.PromotionListDetailResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定URI参数
	if err = c.ShouldBindUri(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}

	// 检查是否是终端用户
	var isEndpoint bool
	if _, exists := c.Get("endpoint"); exists {
		isEndpoint = true
	}

	// 调用服务
	resp, err = p.promotionListSvc.GetDetail(
		c,
		promotion_convertor.NewPromotionListDetailConvertor().ClientToEntity(&req),
		isEndpoint,
	)
}

// UploadReceipt 上传回执
func (p *promotionList) UploadReceipt(c *gin.Context) {
	var (
		req  client.ReceiptUploadRequest
		err  error
		resp dto.ReceiptUploadResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 绑定URI参数
	if err = c.ShouldBindUri(&req); err != nil {
		err = errors.Wrap(err, "bind uri error")
		return
	}

	// 绑定JSON参数
	var jsonReq struct {
		Receipt string `json:"receipt" binding:"required"`
	}
	if err = c.ShouldBindJSON(&jsonReq); err != nil {
		err = errors.Wrap(err, "bind json error")
		return
	}
	req.Receipt = jsonReq.Receipt

	// 获取终端信息
	var endpoint int
	if endpointVal, exists := c.Get("endpoint"); exists {
		if endpointInfo, ok := endpointVal.(map[string]interface{}); ok {
			if id, ok := endpointInfo["id"].(int); ok {
				endpoint = id
			} else {
				err = errors.New("unauthorized")
				return
			}
		} else {
			err = errors.New("unauthorized")
			return
		}
	} else {
		err = errors.New("unauthorized")
		return
	}

	// 调用服务
	err = p.promotionListSvc.UploadReceipt(
		c,
		promotion_convertor.NewReceiptUploadConvertor().ClientToEntity(&req, endpoint),
	)

	if err == nil {
		resp = dto.ReceiptUploadResp{
			Message: "上传成功",
		}
	}
}
