package broken_screen

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"io"
	"marketing-app/internal/api/client/broken_screen/config"
	"marketing-app/internal/pkg/log"
	"net/http"
	"net/url"
	"time"
)

type BrokenScreenClient struct {
	httpClient *http.Client
	cfg        *config.AppConfig
}

func NewBrokenScreenClient(cfg *config.AppConfig) *BrokenScreenClient {
	return &BrokenScreenClient{
		httpClient: &http.Client{
			Timeout: cfg.HTTPTimeout,
		},
		cfg: cfg,
	}
}

// CheckHasScreenInsurance 调用repair api获取条码碎屏险信息
func (c *BrokenScreenClient) CheckHasScreenInsurance(ctx context.Context, barcode string) (interface{}, error) {
	params := c.getRepairParam()
	params["barcode"] = barcode

	values := url.Values{}
	for k, v := range params {
		values.Set(k, v)
	}

	urlStr := fmt.Sprintf("%s/broken_screen_insurance/check?%s", c.cfg.Host, values.Encode())

	req, err := http.NewRequestWithContext(ctx, "GET", urlStr, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request execution failed: %w", err)
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}
	var data map[string]interface{}
	if err = json.Unmarshal(body, &data); err != nil {
		return nil, fmt.Errorf("failed to unmarshal api response: %w", err)
	}

	if ok, exists := data["ok"]; exists && ok == float64(1) {
		return data["data"], nil
	}
	log.Info(fmt.Sprintf("查询条码[%s]碎屏险失败: %v", barcode, data["msg"].(string)))
	return nil, nil
}

// ScreenInsuranceRefund 调用repair api退保碎屏保订单
func (c *BrokenScreenClient) ScreenInsuranceRefund(ctx context.Context, sn string) (interface{}, error) {
	urlStr := fmt.Sprintf("%s/broken_screen_insurance/refund", c.cfg.Host)
	param := c.getRepairParam()
	param["order_sn"] = sn

	values := url.Values{}
	for k, v := range param {
		values.Set(k, v)
	}
	body := values.Encode()

	req, err := http.NewRequestWithContext(ctx, "POST", urlStr, bytes.NewBufferString(body))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request execution failed: %w", err)
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var data map[string]interface{}
	if err := json.Unmarshal(respBody, &data); err != nil {
		return nil, fmt.Errorf("failed to unmarshal api response: %w", err)
	}

	var ret interface{}
	if ok, exists := data["ok"]; exists && ok == float64(1) {
		ret = data["data"]
	} else {
		// 退保失败，返回错误信息
		if msg, exists := data["msg"]; exists {
			return nil, fmt.Errorf("refund failed: %v", msg)
		}
		return nil, fmt.Errorf("refund failed with unknown error")
	}

	return ret, nil
}

func (c *BrokenScreenClient) ScreenInsuranceExchange(ctx *gin.Context, barcode, barcodeNew, orderSn string) (interface{}, error) {
	urlStr := fmt.Sprintf("%s/broken_screen_insurance/exchange", c.cfg.Host)
	param := c.getRepairParam()
	param["order_sn"] = orderSn
	param["old_barcode"] = barcode
	param["new_barcode"] = barcodeNew

	form := url.Values{}
	for k, v := range param {
		form.Set(k, v)
	}
	req, err := http.NewRequestWithContext(ctx, "POST", urlStr, bytes.NewBufferString(form.Encode()))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	resp, err := c.httpClient.Do(req)
	if err != nil {
		log.Info(fmt.Sprintf("请求 %s 失败：%v", urlStr, zap.Error(err)))
		// TODO: sendWXWorkMsg()
		return nil, err
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	var data map[string]interface{}
	if err = json.Unmarshal(body, &data); err != nil {
		return nil, err
	}
	if ok, exists := data["ok"]; exists && ok == float64(1) {
		log.Info(fmt.Sprintf("碎屏保换保成功: %+v", data))
		return data["data"], nil
	}
	log.Info(fmt.Sprintf("碎屏保换保失败: %s", data["msg"].(string)))
	// TODO: sendWXWorkMsg(碎屏保换保失败)
	return nil, nil
}

func md5Sum(s string) [16]byte {
	return md5.Sum([]byte(s))
}

// getRepairParam 获取repair API的通用参数
func (c *BrokenScreenClient) getRepairParam() map[string]string {
	appid := c.cfg.AppID
	appKey := c.cfg.AppKey
	deviceItem := []string{"1", "", "", appid, "", ""}
	deviceID := ""
	for i, v := range deviceItem {
		if i > 0 {
			deviceID += "/"
		}
		deviceID += v
	}
	t := time.Now().Unix()
	sn := fmt.Sprintf("%x", md5Sum(deviceID+appKey+fmt.Sprintf("%d", t)))
	return map[string]string{
		"t":  fmt.Sprintf("%d", t),
		"ua": deviceID,
		"sn": sn,
	}
}
