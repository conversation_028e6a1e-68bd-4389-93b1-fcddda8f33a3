package warranty_exchange

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"marketing-app/internal/handler"
	"marketing-app/internal/handler/warranty/warranty_exchange/dto"
	"marketing-app/internal/pkg/convertor/warranty_convertor"
	"marketing-app/internal/pkg/utils"
	"marketing-app/internal/router/warranty/warranty_exchange/client"
	service "marketing-app/internal/service/warranty/warranty_exchange"
	"time"
)

type WarrantyExchangeHandler interface {
	ExchangeWarranty(c *gin.Context)
}

type Warranty struct {
	warrantySvc service.WarrantyExchangeService
}

func NewWarrantyHandler(svc service.WarrantyExchangeService) *Warranty {
	return &Warranty{
		warrantySvc: svc,
	}
}

func (w *Warranty) ExchangeWarranty(c *gin.Context) {
	var (
		req  client.ExchangeWarrantyRequest
		err  error
		resp dto.ExchangeWarrantyResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}
	// 测试环境下通过json直接传uid，发布模式下通过传token并且经过auth后从context中获取
	if gin.Mode() == gin.ReleaseMode {
		req.Uid = c.MustGet("uid").(int)
	}
	if err = utils.DateFormatValidate(time.DateOnly, req.ExchangeDate); err != nil {
		err = errors.Wrap(err, "date format error")
		return
	}
	err = w.warrantySvc.ExchangeWarranty(
		c,
		warranty_convertor.NewWarrantyExchangeConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}
	resp = dto.ExchangeWarrantyResp{
		Message: fmt.Sprint("换机成功！"),
	}
}
