package dto

import "time"

type CreateWarranty struct {
	Id              int     `json:"id,omitempty"`
	Barcode         string  `json:"barcode,omitempty"`
	ExtBarcode      string  `json:"ext_barcode,omitempty"`
	Number          string  `json:"number,omitempty"`
	Salesman        string  `json:"salesman,omitempty"`
	CustomerName    string  `json:"customer_name,omitempty"`
	CustomerPhone   string  `json:"customer_phone,omitempty"`
	CustomerAddress string  `json:"customer_addr,omitempty"`
	ModelId         int     `json:"model_id,omitempty"`
	Model           string  `json:"model,omitempty"`
	Endpoint        int     `json:"endpoint_id,omitempty"`
	BuyDate         string  `json:"buy_date,omitempty"`
	ProductDate     string  `json:"product_date,omitempty"`
	StudentName     string  `json:"student_name,omitempty"`
	StudentSchool   string  `json:"student_school,omitempty"`
	StudentGrade    string  `json:"student_grade,omitempty"`
	StudentBirthday string  `json:"student_birthday,omitempty"`
	CreatedAt       string  `json:"created_at,omitempty"`
	CustomerPrice   float64 `json:"customer_price,omitempty"`
	CustomerSex     string  `json:"customer_sex,omitempty"`
	StudentSex      string  `json:"student_sex,omitempty"`
	SalesmanId      int     `json:"salesman_id,omitempty"`

	EndpointName    string `json:"endpoint_name,omitempty"`
	EndpointAddress string `json:"endpoint_address,omitempty"`
	EndpointPhone   string `json:"endpoint_phone,omitempty"`
	EndpointManager string `json:"endpoint_manager,omitempty"`
}

type GetWarranty struct {
	Id int
}

type WarrantyMachineType struct {
	ID             int        `json:"id"`
	Barcode        string     `json:"barcode"`
	Number         string     `json:"number"`
	Imei           string     `json:"imei"`
	ExtBarcode     string     `json:"ext_barcode"`
	CreatedAt      *time.Time `json:"created_at"`
	Status         int8       `json:"status"` // -1-虚卡翻新，0-虚卡，1-正常，2-换机，3-退机，4-其他，5-翻新
	Model          string     `json:"model"`
	ModelID        int        `json:"model_id"`
	CustomerPhone  string     `json:"customer_phone"`
	ProductDate    *time.Time `json:"product_date"`
	State          int8       `json:"state"`
	ActivatedAtOld *time.Time `json:"activated_at_old"`

	CategoryId    int `json:"category_id"`     // machine_type表 分类id
	ExtBarcodeNum int `json:"ext_barcode_num"` // machine_type表 副机条码数量
}
