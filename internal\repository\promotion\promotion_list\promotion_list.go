package promotion_list

import (
	"marketing-app/internal/model"
	"marketing-app/internal/repository/promotion/promotion_list/dto"
	"marketing-app/internal/service/promotion/promotion_list/entity"
	"sort"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type PromotionList interface {
	GetList(c *gin.Context, param *entity.PromotionListsReq, isEndpoint bool) ([]dto.PromotionListItem, int64, error)
	GetDetail(c *gin.Context, id int, isEndpoint bool) (*dto.PromotionListDetail, error)
	UpdateReceipt(c *gin.Context, id, endpoint int, receipt string) error
	GetPromotionDetail(c *gin.Context, id int) (*model.SalesPromotion, error)
	GetReceiptsByListIds(c *gin.Context, ids []int, isEndpoint bool) ([]dto.PromotionListReceipt, error)
	GetWarrantyInfoByIds(c *gin.Context, warrantyIds []int) ([]dto.WarrantyInfo, error)
}

type promotionList struct {
	db *gorm.DB
}

func NewPromotionList(db *gorm.DB) PromotionList {
	return &promotionList{
		db: db,
	}
}

func (p *promotionList) GetList(c *gin.Context, param *entity.PromotionListsReq, isEndpoint bool) ([]dto.PromotionListItem, int64, error) {
	var result []dto.PromotionListItem
	var total int64

	query := p.db.WithContext(c).Table("sales_promotion_list l").
		Select(`l.*, 
			CONCAT(rp.region_name, rc.region_name) as region_name,
			a.name as agency_name,
			e.name as endpoint_name,
			e.code as endpoint_code,
			e.manager,
			e.phone as endpoint_phone,
			e.address`).
		Joins("LEFT JOIN endpoint e ON l.endpoint = e.id").
		Joins("LEFT JOIN agency a ON e.top_agency = a.id").
		Joins("LEFT JOIN region rp ON e.province = rp.region_id").
		Joins("LEFT JOIN region rc ON e.city = rc.region_id").
		Joins("LEFT JOIN warranty_return wr ON l.warranty_id = wr.warranty_id").
		Where("l.sales_promotion_id = ?", param.Id).
		Where("wr.id IS NULL")

	// 添加筛选条件
	if param.Agency > 0 {
		query = query.Where("e.top_agency = ?", param.Agency)
	}
	if param.Endpoint > 0 {
		query = query.Where("l.endpoint = ?", param.Endpoint)
	}
	if param.Receipt > 0 {
		if param.Receipt == 1 {
			query = query.Where("l.is_receipt = 1")
		} else if param.Receipt == 2 {
			query = query.Where("l.is_receipt = 0")
		}
	}
	if len(param.ModelId) > 0 {
		query = query.Where("l.model_id IN ?", param.ModelId)
	}
	if param.Keyword != "" {
		query = query.Where("l.barcode LIKE ?", "%"+param.Keyword+"%")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	offset := (param.Page - 1) * param.PageSize
	if err := query.Order("l.buy_date").
		Offset(offset).
		Limit(param.PageSize).
		Find(&result).Error; err != nil {
		return nil, 0, err
	}

	return result, total, nil
}

func (p *promotionList) GetDetail(c *gin.Context, id int, isEndpoint bool) (*dto.PromotionListDetail, error) {
	var result dto.PromotionListDetail

	err := p.db.WithContext(c).Table("sales_promotion_list l").
		Select(`l.*,
			w.student_uid,
			w.student_name,
			a.name as agency_name,
			w.activated_at_old as activated_at,
			w.number,
			TIMESTAMPDIFF(HOUR, w.buy_date, w.activated_at_old) as hour_interval,
			e.code as endpoint_code,
			e.name as endpoint_name,
			e.manager,
			wr.return_at`).
		Joins("LEFT JOIN warranty w ON l.warranty_id = w.id").
		Joins("LEFT JOIN endpoint e ON e.id = w.endpoint").
		Joins("LEFT JOIN agency a ON e.top_agency = a.id").
		Joins("LEFT JOIN region rp ON e.province = rp.region_id").
		Joins("LEFT JOIN region rc ON e.city = rc.region_id").
		Joins("LEFT JOIN warranty_return wr ON w.barcode = wr.barcode").
		Where("l.id = ?", id).
		Group("l.barcode").
		First(&result).Error

	if err != nil {
		return nil, err
	}

	return &result, nil
}

func (p *promotionList) UpdateReceipt(c *gin.Context, id, endpoint int, receipt string) error {
	return p.db.WithContext(c).Transaction(func(tx *gorm.DB) error {
		// 更新促销列表
		if err := tx.Model(&model.SalesPromotionList{}).
			Where("id = ?", id).
			Updates(map[string]interface{}{
				"receipt_at":  gorm.Expr("NOW()"),
				"is_receipt":  1,
			}).Error; err != nil {
			return err
		}

		// 删除旧的回执
		if err := tx.Where("sales_promotion_list_id = ? AND type = 1", id).
			Delete(&model.SalesPromotionListReceipt{}).Error; err != nil {
			return err
		}

		// 插入新的回执
		receiptSlice := strings.Split(receipt, ",")
		var receipts []model.SalesPromotionListReceipt
		for _, item := range receiptSlice {
			// 处理number
			number := ""
			parts := strings.Split(item, "/")
			if len(parts) > 0 {
				lastPart := parts[len(parts)-1]
				subParts := strings.Split(lastPart, "_")
				if len(subParts) > 0 {
					number = subParts[0]
				}
			}

			receipts = append(receipts, model.SalesPromotionListReceipt{
				SalesPromotionListId: id,
				Receipt:              item,
				Number:               number,
				Type:                 1,
			})
		}

		if err := tx.Create(&receipts).Error; err != nil {
			return err
		}

		return nil
	})
}

func (p *promotionList) GetPromotionDetail(c *gin.Context, id int) (*model.SalesPromotion, error) {
	var promotion model.SalesPromotion
	err := p.db.WithContext(c).Where("id = ?", id).First(&promotion).Error
	return &promotion, err
}

func (p *promotionList) GetReceiptsByListIds(c *gin.Context, ids []int, isEndpoint bool) ([]dto.PromotionListReceipt, error) {
	var receipts []dto.PromotionListReceipt

	if len(ids) == 0 {
		return receipts, nil
	}

	if isEndpoint {
		// 终端模式：返回合并的回执字符串
		err := p.db.WithContext(c).Table("sales_promotion_list_receipt").
			Select("sales_promotion_list_id, GROUP_CONCAT(receipt) AS receipt").
			Where("sales_promotion_list_id IN ?", ids).
			Group("sales_promotion_list_id").
			Find(&receipts).Error
		return receipts, err
	} else {
		// 后台模式：返回详细的回执信息
		err := p.db.WithContext(c).Table("sales_promotion_list_receipt r").
			Select("r.sales_promotion_list_id, r.receipt, r.number, c.count").
			Joins("LEFT JOIN (SELECT COUNT(*) as count, number FROM sales_promotion_list_receipt WHERE deleted_at IS NULL GROUP BY number) c ON c.number = r.number").
			Where("r.sales_promotion_list_id IN ?", ids).
			Find(&receipts).Error
		return receipts, err
	}
}

func (p *promotionList) GetWarrantyInfoByIds(c *gin.Context, warrantyIds []int) ([]dto.WarrantyInfo, error) {
	var warranties []dto.WarrantyInfo
	err := p.db.WithContext(c).Table("warranty").
		Select("id, number, activated_at_old, student_name").
		Where("id IN ?", warrantyIds).
		Find(&warranties).Error
	return warranties, err
}

// RepeatedCount 用于排序的类型
type RepeatedCount []dto.PromotionListReceipt

func (r RepeatedCount) Len() int           { return len(r) }
func (r RepeatedCount) Swap(i, j int)      { r[i], r[j] = r[j], r[i] }
func (r RepeatedCount) Less(i, j int) bool { return r[i].Count > r[j].Count }

// SortReceiptsByCount 按count排序回执
func SortReceiptsByCount(receipts []dto.PromotionListReceipt) {
	sort.Sort(RepeatedCount(receipts))
}
