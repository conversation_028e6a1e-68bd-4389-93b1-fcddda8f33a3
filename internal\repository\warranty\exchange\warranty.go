package exchange

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"marketing-app/internal/consts"
	"marketing-app/internal/model"
	"marketing-app/internal/pkg/log"
	"marketing-app/internal/repository/warranty/exchange/builder"
	"marketing-app/internal/repository/warranty/exchange/dto"
	"marketing-app/internal/service/warranty/warranty_exchange/entity"
	"time"
)

type Warranty interface {
	Get(c *gin.Context, query *builder.Warranty) (data *dto.Warranty, err error)
	Update(c *gin.Context, warranty *dto.Warranty, newWarranty *dto.Warranty, param *entity.Warranty, machine map[string]interface{}, endpointId int) error
	Create(c *gin.Context, warranty *dto.Warranty, param *entity.Warranty, machine map[string]interface{}, endpointId int) error
	CreateVirtualCard(c *gin.Context, cond *builder.Warranty, create *model.Warranty) error
}

type warranty struct {
	db *gorm.DB
}

func NewWarranty(db *gorm.DB) Warranty {
	return &warranty{
		db: db,
	}
}

func (w *warranty) UseTransaction(tx *gorm.DB) *gorm.DB {
	if tx == nil {
		return w.db
	}
	return tx
}

func (w *warranty) Get(c *gin.Context, query *builder.Warranty) (data *dto.Warranty, err error) {
	err = query.Fill(w.db.WithContext(c).Model(&model.Warranty{})).First(&data).Error
	return
}

func (w *warranty) Update(
	c *gin.Context,
	warranty *dto.Warranty,
	newWarranty *dto.Warranty,
	param *entity.Warranty,
	machine map[string]interface{},
	endpointId int,
) error {
	return w.db.Transaction(func(tx *gorm.DB) (err error) {
		defer func() {
			if err != nil {
				log.Warn("exchange warranty by update transaction err", zap.Error(err))
			}
		}()

		return w.update(c, tx, warranty, newWarranty, param, machine, endpointId)
	})
}

func (w *warranty) Create(
	c *gin.Context,
	warranty *dto.Warranty,
	param *entity.Warranty,
	machine map[string]interface{},
	endpointId int,
) error {
	return w.db.Transaction(func(tx *gorm.DB) (err error) {
		defer func() {
			if err != nil {
				log.Warn("exchange warranty by create transaction err", zap.Error(err))
			}
		}()

		return w.create(c, tx, warranty, param, machine, endpointId)
	})
}

func (w *warranty) update(c *gin.Context, tx *gorm.DB, warranty *dto.Warranty, newWarranty *dto.Warranty, param *entity.Warranty, machine map[string]interface{}, endpointId int) (err error) {
	now := time.Now()
	// 更新虚卡为正常新保卡
	updateData := map[string]interface{}{
		"salesman":         warranty.Salesman,
		"salesman_id":      warranty.SalesmanId,
		"customer_price":   warranty.CustomerPrice,
		"customer_name":    warranty.CustomerName,
		"customer_sex":     warranty.CustomerSex,
		"customer_phone":   warranty.CustomerPhone,
		"customer_addr":    warranty.CustomerAddr,
		"endpoint":         endpointId,
		"buy_date":         warranty.BuyDate,
		"warranty_period":  warranty.WarrantyPeriod,
		"lng":              warranty.Lng,
		"lat":              warranty.Lat,
		"student_uid":      warranty.StudentUID,
		"student_name":     warranty.StudentName,
		"student_sex":      warranty.StudentSex,
		"student_school":   warranty.StudentSchool,
		"student_grade":    warranty.StudentGrade,
		"student_birthday": warranty.StudentBirthday,
		"created_at":       now,
		"created_at_new":   nil,
		"updated_at":       now,
		"status":           consts.WarrantyStatusActive, // 正常状态
		"number":           machine["number"].(string),
		"imei":             warranty.Imei,
		"ext_barcode":      "",
		"purchase_way":     warranty.PurchaseWay,
		"assessment":       warranty.Assessment,
	}
	err = tx.WithContext(c).Model(&model.Warranty{}).Where("barcode = ? AND id = ?", param.NewBarcode, newWarranty.Id).Updates(updateData).Error
	if err != nil {
		return
	}
	// 标记旧保卡
	err = tx.WithContext(c).Model(&model.Warranty{}).Where("id = ?", warranty.Id).Updates(map[string]interface{}{
		"status":     consts.WarrantyStatusExchanged,
		"updated_at": now,
		"deleted_at": now,
	}).Error
	if err != nil {
		return
	}
	// 插入换货记录
	exchange := &model.WarrantyExchange{
		Barcode:       param.Barcode,
		BarcodeNew:    param.NewBarcode,
		Reason:        param.Reason,
		WarrantyID:    warranty.Id,
		WarrantyNewID: newWarranty.Id,
		UID:           param.Uid,
		Endpoint:      endpointId,
		ExchangedAt:   param.ExchangeDate,
	}
	err = tx.WithContext(c).Create(&exchange).Error
	if err != nil {
		return err
	}
	// 删除微信绑定
	var binding *model.PuzcWxbinding
	err = tx.WithContext(c).Where("barcode = ?", param.Barcode).
		Delete(&binding).
		Error
	if err != nil {
		return
	}
	// TODO: 退货换货发送消息队列 （需要go 1.23.0） warrantyReturnOrExchangeProducer()

	return nil
}

func (w *warranty) create(c *gin.Context, tx *gorm.DB, warranty *dto.Warranty, param *entity.Warranty, machine map[string]interface{}, endpointId int) (err error) {
	now := time.Now()
	warrantyId := warranty.Id
	// 新保卡数据
	realSale := 0
	warranty.Id = 0
	warranty.Barcode = param.NewBarcode
	warranty.ExtBarcode = ""
	warranty.CreatedAt = &now
	warranty.UpdatedAt = &now
	warranty.Number = machine["number"].(string)
	warranty.WarrantyPeriod = param.ExchangeDate
	warranty.State = 1
	warranty.RealSale = &realSale
	// 插入新保卡
	pending := warranty.ToModelWarranty()
	err = tx.WithContext(c).Model(&model.Warranty{}).Create(&pending).Error
	if err != nil {
		return
	}
	warrantyNewID := pending.ID

	// 标记旧保卡
	err = tx.WithContext(c).Model(&model.Warranty{}).Where("id = ?", warrantyId).
		Updates(map[string]interface{}{
			"status":     consts.WarrantyStatusExchanged,
			"updated_at": now,
			"deleted_at": now,
		}).Error
	if err != nil {
		return
	}

	// 插入换货记录
	var exchange *model.WarrantyExchange
	exchange = &model.WarrantyExchange{
		Barcode:       param.Barcode,
		BarcodeNew:    param.NewBarcode,
		Reason:        param.Reason,
		WarrantyID:    warrantyId,
		WarrantyNewID: warrantyNewID,
		UID:           param.Uid,
		Endpoint:      endpointId,
		ExchangedAt:   param.ExchangeDate,
		CreatedAt:     &now,
	}
	err = tx.WithContext(c).Model(&model.WarrantyExchange{}).Create(&exchange).Error
	if err != nil {
		return
	}
	// 删除微信绑定
	err = tx.WithContext(c).Where("barcode = ?", param.Barcode).Delete(&model.PuzcWxbinding{}).Error
	if err != nil {
		return
	}
	// TODO: 退货换货发送消息队列 （需要go 1.23.0） warrantyReturnOrExchangeProducer()
	return nil
}

func (w *warranty) CreateVirtualCard(c *gin.Context, cond *builder.Warranty, create *model.Warranty) error {
	return cond.Fill(w.db.WithContext(c).Model(&model.Warranty{})).Create(create).Error
}
