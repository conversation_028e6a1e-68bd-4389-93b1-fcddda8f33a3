package dto

type WecomLoginReq struct {
	Code string `json:"code" form:"code" binding:"required"`
}

type PhoneLoginReq struct {
	Phone    string `json:"phone" form:"phone" binding:"required"`
	Code     string `json:"code" form:"code" binding:"required"`
	DeviceID string `json:"device_id" form:"device_id"`
}

type UserInfoResp struct {
	ID           uint     `json:"id"`
	Avatar       string   `json:"avatar"`
	Username     string   `json:"username"`
	Name         string   `json:"name"`
	Phone        string   `json:"phone"`
	Token        string   `json:"token"`
	Expires      int64    `json:"expires"`
	RefreshToken string   `json:"refreshToken"`
	Roles        []string `json:"roles"`
	Permissions  []string `json:"permissions"`
}
