package districts

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"marketing-app/internal/api/client/districts/config"
	"marketing-app/internal/pkg/utils"
	"net/http"
	"net/url"
	"strconv"
	"time"
)

type DistrictClient struct {
	httpClient *http.Client
	cfg        *config.AppConfig
}

func NewDistrictClient(cfg *config.AppConfig) *DistrictClient {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}

	return &DistrictClient{
		httpClient: &http.Client{
			Timeout:   cfg.HTTPTimeout,
			Transport: transport,
		},
		cfg: cfg,
	}
}

func (c *DistrictClient) GetDistrictsData(ctx context.Context, pid string, level int) (map[string]interface{}, error) {
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	sn := utils.GenerateSN(c.cfg.DeviceID, c.cfg.AppSecret, timestamp)

	baseURL := c.cfg.Host + "/districts"
	params := url.Values{}
	params.Add("pid", pid)
	params.Add("level", strconv.Itoa(level))
	params.Add("t", timestamp)
	params.Add("device_id", c.cfg.DeviceID)
	params.Add("sn", sn)

	fullURL := baseURL + "?" + params.Encode()

	req, err := http.NewRequestWithContext(ctx, "GET", fullURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var data map[string]interface{}
	if err = json.Unmarshal(body, &data); err != nil {
		return nil, fmt.Errorf("failed to unmarshal api response: %w", err)
	}

	return data, nil
}
