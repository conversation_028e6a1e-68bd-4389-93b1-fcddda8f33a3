package client

// BugfreeEvaluationsRequest 问题反馈评价请求
type BugfreeEvaluationsRequest struct {
	BugId      int    `json:"id" binding:"required"`        // 问题反馈ID
	Satisfied  int    `json:"satisfied" binding:"required"` // 是否满意：1满意，0不满意
	Score      int    `json:"score"`                        // 评分
	Remark     string `json:"remark"`                       // 备注
	Activation int    `json:"activation"`                   // 是否激活：1激活，0不激活
}
