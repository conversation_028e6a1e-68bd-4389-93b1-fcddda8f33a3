package warranty_return

import (
	"marketing-app/internal/api/client/broken_screen"
	"marketing-app/internal/api/client/broken_screen/config"
	"marketing-app/internal/cache"
	"marketing-app/internal/consts"
	"marketing-app/internal/model"
	"marketing-app/internal/pkg/log"
	"marketing-app/internal/pkg/utils"
	contactRepo "marketing-app/internal/repository/contact"
	contactBuilder "marketing-app/internal/repository/contact/builder"
	endpointRepo "marketing-app/internal/repository/endpoint/endpoint"
	userEndpointRepo "marketing-app/internal/repository/endpoint/user_endpoint"
	machineRepo "marketing-app/internal/repository/machine"
	drpMachineBuilder "marketing-app/internal/repository/machine/drp_machine/builder"
	machineTypeBuilder "marketing-app/internal/repository/machine/machine_type/builder"
	prototypeRepo "marketing-app/internal/repository/prototype"
	prototypeBuilder "marketing-app/internal/repository/prototype/builder"
	returnRepo "marketing-app/internal/repository/warranty/returns"
	"marketing-app/internal/repository/warranty/returns/builder"
	"marketing-app/internal/repository/warranty/returns/dto"
	"marketing-app/internal/service/warranty/warranty_return/entity"
	"strings"
	"sync"
	"time"

	"gorm.io/gorm"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

type WarrantyReturnService interface {
	ReturnWarranty(c *gin.Context, param *entity.Warranty) (err error)
}

type warrantyReturn struct {
	repo               returnRepo.Warranty
	userEndpointRepo   userEndpointRepo.UserEndpoint
	endpointRepo       endpointRepo.Endpoint
	contactRepo        contactRepo.Contact
	prototypeRepo      prototypeRepo.Prototype
	prototypeCache     cache.PrototypeCache
	machineRepo        machineRepo.Machine
	brokenScreenClient *broken_screen.BrokenScreenClient
}

func NewWarrantyReturn(
	repo returnRepo.Warranty,
	userEndpointRepo userEndpointRepo.UserEndpoint,
	endpointRepo endpointRepo.Endpoint,
	contactRepo contactRepo.Contact,
	prototypeRepo prototypeRepo.Prototype,
	machineRepo machineRepo.Machine,
	prototypeCache cache.PrototypeCache,
) WarrantyReturnService {
	// 初始化broken screen客户端
	cfg := config.LoadBrokenScreenConfig()
	brokenScreenClient := broken_screen.NewBrokenScreenClient(cfg)

	return &warrantyReturn{
		repo:               repo,
		userEndpointRepo:   userEndpointRepo,
		endpointRepo:       endpointRepo,
		contactRepo:        contactRepo,
		prototypeRepo:      prototypeRepo,
		prototypeCache:     prototypeCache,
		machineRepo:        machineRepo,
		brokenScreenClient: brokenScreenClient,
	}
}

func (w *warrantyReturn) ReturnWarranty(c *gin.Context, param *entity.Warranty) (err error) {
	now := time.Now()
	endpoint, err := w.userEndpointRepo.GetEndpointAgencyByUid(c, param.Uid)
	if endpoint == nil {
		err = errors.New("无权限操作")
		return
	}
	returnAt, err := w.calReturnDate(c, endpoint.ID, param.ReturnDate)
	if err != nil {
		return
	}
	warranty, isOverdue, err := w.validateReturnWarranty(c, param, returnAt, endpoint.ID)
	if err != nil {
		return
	}
	err = w.repo.Return(
		c,
		builder.NewWarranty().IdEq(warranty.Id),
		&model.WarrantyReturn{
			Barcode:    param.Barcode,
			Reason:     param.Reason,
			WarrantyID: warranty.Id,
			UID:        param.Uid,
			Endpoint:   endpoint.ID,
			ReturnAt:   returnAt,
			IsOverdue:  isOverdue,
			CreatedAt:  &now,
		},
		map[string]interface{}{
			"status":     consts.WarrantyStatusReturned,
			"updated_at": now,
			"deleted_at": now,
		},
	)
	if err != nil {
		err = errors.New("退货操作失败")
		return
	}

	// 异步任务
	numTasks := 2
	errCh := make(chan error, numTasks)
	var wg sync.WaitGroup
	wg.Add(numTasks)
	// 更新保单联系人信息
	go func() {
		defer wg.Done()
		errCh <- w.loadContact(c, warranty)
	}()
	// 添加虚拟保卡
	go func() {
		defer wg.Done()
		errCh <- w.addVirtualCard(c, warranty)
	}()
	wg.Wait()
	close(errCh)
	// 收集协程中的错误
	var collectedErrs []string
	for err = range errCh {
		if err != nil {
			collectedErrs = append(collectedErrs, err.Error())
		}
	}
	if len(collectedErrs) > 0 {
		err = errors.New(strings.Join(collectedErrs, " "))
		return
	}
	// 保卡退换机添加样机
	returnType, err := w.createPrototype(c, param, warranty)
	if err != nil {
		return err
	}
	// 进销存同步
	err = w.machineRepo.DrpMachine(
		c,
		drpMachineBuilder.NewDrpMachine().
			BarcodeEq(param.Barcode).
			StatusOmit(consts.MachineStatusInactive, consts.MachineStatusOutOfStock).
			OperationTypeEq(3).
			ReturnEq(returnType).
			WarrantyEq(warranty.ToDrpMachineWarranty()),
	)
	// 碎屏保退款
	if data, _ := w.CheckHasScreenInsurance(c, param.Barcode); data != nil {
		if parsedData, ok := data.(map[string]interface{}); ok && parsedData["sn"].(string) != "" {
			sn := parsedData["sn"].(string)
			if refund, _ := w.ScreenInsuranceRefund(c, sn); refund == nil {
				log.Warn("退机成功，但碎屏保退款失败，请联系相关人员")
			}
		}
	}
	return nil
}

func (w *warrantyReturn) calReturnDate(c *gin.Context, endpointId int, returnAt *time.Time) (*time.Time, error) {
	now := time.Now()
	can, err := w.endpointRepo.CheckPermission(c, endpointId, "warranty-tuihuan-time")
	if err != nil {
		return nil, err
	}
	if !can {
		return &now, nil
	}
	newReturnAt := returnAt.Add(23*time.Hour + 59*time.Minute + 59*time.Second)
	return &newReturnAt, nil
}

// CheckHasScreenInsurance 调用repair api获取条码碎屏险信息
func (w *warrantyReturn) CheckHasScreenInsurance(c *gin.Context, barcode string) (interface{}, error) {
	return w.brokenScreenClient.CheckHasScreenInsurance(c, barcode)
}

// ScreenInsuranceRefund 调用repair api退保碎屏保订单
func (w *warrantyReturn) ScreenInsuranceRefund(c *gin.Context, sn string) (interface{}, error) {
	return w.brokenScreenClient.ScreenInsuranceRefund(c, sn)
}

func (w *warrantyReturn) validateReturnWarranty(c *gin.Context, param *entity.Warranty, returnAt *time.Time, endpointId int) (warranty *dto.Warranty, isOverdue bool, err error) {
	if !utils.IsBarcode(param.Barcode) {
		err = errors.New("条码格式错误")
		return
	}
	warranty, err = w.repo.Get(c, builder.NewWarranty().BarcodeEq(param.Barcode).StatusIn(consts.WarrantyStatusActive))
	if err != nil {
		err = errors.New("旧机器电子保卡不存在")
		return
	}

	if warranty.Endpoint != endpointId {
		err = errors.New("此保卡非此终端录入，无权限操作")
		return
	}
	if warranty.BuyDate.Add(7 * 24 * time.Hour).Before(*returnAt) {
		err = errors.New("已经超出退货有效期")
		return
	}
	if warranty.Type == consts.WarrantyTypeFrozen {
		err = errors.New("此保卡已冻结，请联系客服部")
		return
	}
	if returnAt.Before(*warranty.BuyDate) {
		err = errors.New("退机时间选择不能早于购买时间")
		return
	}
	if returnAt.After(time.Now()) {
		err = errors.New("退机时间选择不能晚于当前时间")
		return
	}
	if warranty.BuyDate.Add(7 * 24 * time.Hour).Before(*returnAt) {
		isOverdue = true
	}
	return warranty, isOverdue, nil
}

func (w *warrantyReturn) loadContact(c *gin.Context, warranty *dto.Warranty) (err error) {
	contactsNumber, err := w.repo.Count(
		c,
		builder.NewWarranty().CustomerPhoneEq(warranty.CustomerPhone).StatusIn(consts.WarrantyStatusActive),
	)
	if err != nil {
		return
	}
	if contactsNumber == 0 {
		return w.contactRepo.Update(
			c,
			contactBuilder.NewContact().EndpointIdEq(warranty.Endpoint).CustomerPhoneEq(warranty.CustomerPhone),
			map[string]interface{}{
				"type":     "lost",
				"sub_type": "returned",
			},
		)
	}
	return nil
}

func (w *warrantyReturn) addVirtualCard(c *gin.Context, warranty *dto.Warranty) (err error) {
	now := time.Now()
	virtual := consts.WarrantyStatusVirtual
	return w.repo.Create(
		c,
		builder.NewWarranty().Omits("created_at", "buy_date"),
		&model.Warranty{
			Number:        warranty.Number,
			Barcode:       warranty.Barcode,
			Imei:          warranty.Imei,
			Status:        &virtual,
			ModelID:       warranty.ModelId,
			Model:         warranty.Model,
			CustomerPrice: warranty.CustomerPrice,
			ProductDate:   warranty.ProductDate,
			CreatedAtNew:  &now,
		},
	)
}

func (w *warrantyReturn) createPrototype(c *gin.Context, param *entity.Warranty, warranty *dto.Warranty) (returnType int, err error) {
	// 检查机型是否可作为样机
	_, err = w.machineRepo.GetMachineType(c, machineTypeBuilder.NewMachineType().PrototypeStatusEq(consts.PrototypeStatusInStock).ModelIdEq(warranty.ModelId))
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("获取机器类型失败")
		return
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return 3, nil
	}
	// 查询样机信息
	prototypeInfo, err := w.prototypeRepo.GetPrototypeMachineTypeConfig(
		c,
		prototypeBuilder.NewPrototype().
			JoinPrototypeConfigEq("LEFT JOIN prototype_config pc ON prototype.model_id = pc.model_id").
			JoinMachineTypeEq("LEFT JOIN machine_type mt ON prototype.model_id = mt.model_id").
			BarcodeEq(param.Barcode). // 判断是否是演示样机
			StatusIn(consts.WarrantyStatusActive),
	)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	if prototypeInfo != nil && prototypeInfo.Status == consts.PrototypeStatusInStock {
		return 1, nil
	}
	// 未找到样机时添加样机
	err = w.prototypeRepo.Create(c,
		&model.Prototype{
			Number:   warranty.Number,
			Barcode:  param.Barcode,
			Imei:     warranty.Imei,
			Status:   consts.PrototypeStatusInStock,
			Endpoint: warranty.Endpoint,
			Model:    warranty.Model,
			ModelID:  warranty.ModelId,
			UserID:   param.Uid,
			Type:     consts.PrototypeTypeReturn,
		},
	)
	if err != nil {
		return
	}
	// 添加样机缓存
	err = w.prototypeCache.Set(c, warranty.Number)
	if err != nil {
		err = errors.Wrap(err, "添加缓存失败")
		return
	}
	return 2, nil
}
