package model

import "time"

type PuzcWxbinding struct {
	Bid      int       `gorm:"column:bid;primaryKey;autoIncrement"`
	Openid   string    `gorm:"column:openid;size:50;not null"`
	Barcode  string    `gorm:"column:barcode;size:32;not null"`
	Bindtime time.Time `gorm:"column:bindtime;default:CURRENT_TIMESTAMP"`
}

// TableName sets the table name for the PuzcWxbinding model
func (PuzcWxbinding) TableName() string {
	return "puzc_wxbinding"
}
