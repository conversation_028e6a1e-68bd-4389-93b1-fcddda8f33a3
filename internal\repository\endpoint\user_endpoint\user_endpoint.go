package user_endpoint

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"marketing-app/internal/repository/endpoint/user_endpoint/dto"
)

type UserEndpoint interface {
	GetEndpointAgencyByUid(c *gin.Context, uid int) (ep *dto.EndpointAgency, err error)
}

type userEndpoint struct {
	db *gorm.DB
}

func NewUserEndpoint(db *gorm.DB) UserEndpoint {
	return &userEndpoint{
		db: db,
	}
}

func (e *userEndpoint) GetEndpointAgencyByUid(c *gin.Context, uid int) (ep *dto.EndpointAgency, err error) {
	err = e.db.WithContext(c).Table("user_endpoint as uep").
		Select("ep.id, ep.name, ep.address, ep.phone, ep.manager, ep.top_agency, ep.second_agency, a.channel").
		Joins("LEFT JOIN endpoint ep ON uep.endpoint = ep.id").
		Joins("LEFT JOIN agency a ON IF(ep.second_agency = 0, ep.top_agency, ep.second_agency) = a.id").
		Where("uep.uid = ? AND ep.status = 1", uid).
		Scan(&ep).Error
	if err != nil {
		return nil, err
	}
	return ep, nil
}
