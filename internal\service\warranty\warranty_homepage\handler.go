package warranty_homepage

import (
	"marketing-app/internal/pkg/utils"
	"marketing-app/internal/repository/warranty/base/dto"
)

type BarcodeSearcher struct {
	BaseSearcher
	warranty *WarrantyBase
}

func (s *BarcodeSearcher) Find(req *SearchRequest) (data []dto.WarrantyDetail, total int64, err error) {
	if utils.IsBarcode(req.Query) {
		data, total, err = s.warranty.GetByBarcode(req.Context, req.Query)
		if len(data) > 0 {
			return
		}
	}
	return s.BaseSearcher.Find(req)
}

type PhoneSearcher struct {
	BaseSearcher
	warranty *WarrantyBase
}

func (s *PhoneSearcher) Find(req *SearchRequest) (data []dto.WarrantyDetail, total int64, err error) {
	if utils.IsPhone(req.Query) {
		data, total, err = s.warranty.GetByPhone(req.Context, req.EndpointId, req.Query)
		if len(data) > 0 {
			return
		}
	}
	return s.BaseSearcher.Find(req)
}

type NumberSearcher struct {
	BaseSearcher
	warranty *WarrantyBase
}

func (s *NumberSearcher) Find(req *SearchRequest) (data []dto.WarrantyDetail, total int64, err error) {
	if utils.IsSerialNumber(req.Query) {
		data, total, err = s.warranty.GetByNumber(req.Context, req.Query)
		if len(data) > 0 {
			return
		}
	}
	return s.BaseSearcher.Find(req)
}

type ImeiSearcher struct {
	BaseSearcher
	warranty *WarrantyBase
}

func (s *ImeiSearcher) Find(req *SearchRequest) (data []dto.WarrantyDetail, total int64, err error) {
	if utils.IsImei(req.Query) {
		data, total, err = s.warranty.GetByImei(req.Context, req.Query)
		if len(data) > 0 {
			return
		}
	}
	return s.BaseSearcher.Find(req)
}
