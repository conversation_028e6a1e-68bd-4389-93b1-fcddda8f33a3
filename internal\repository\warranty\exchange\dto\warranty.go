package dto

import (
	"marketing-app/internal/model"
	drpMachineDto "marketing-app/internal/repository/machine/drp_machine/dto"
	"time"
)

type Warranty struct {
	Id              int
	Type            int
	BuyDate         *time.Time
	Endpoint        int
	CustomerPhone   string
	Barcode         string
	ExtBarcode      string
	Status          int
	State           int
	ActivatedAtOld  *time.Time
	Number          string
	Imei            string
	Model           string
	ModelId         int
	CustomerPrice   float64
	ProductDate     *time.Time
	CreatedAt       *time.Time
	Salesman        string
	SalesmanId      int
	CustomerName    string
	CustomerSex     string
	CustomerAddr    string
	WarrantyPeriod  *time.Time
	Lng             string
	Lat             string
	StudentUID      int
	StudentName     string
	StudentSex      string
	StudentSchool   string
	StudentGrade    string
	StudentBirthday *time.Time
	UpdatedAt       *time.Time
	PurchaseWay     string
	Assessment      int
	RealSale        *int `gorm:"column:realsale"`
}

func (w *Warranty) ToDrpMachineWarranty() *drpMachineDto.Warranty {
	return &drpMachineDto.Warranty{
		Id:              w.Id,
		Type:            w.Type,
		BuyDate:         w.BuyDate,
		Endpoint:        w.Endpoint,
		CustomerPhone:   w.CustomerPhone,
		Barcode:         w.Barcode,
		ExtBarcode:      w.ExtBarcode,
		Status:          w.Status,
		State:           w.State,
		ActivatedAtOld:  w.ActivatedAtOld,
		Number:          w.Number,
		Imei:            w.Imei,
		Model:           w.Model,
		ModelId:         w.ModelId,
		CustomerPrice:   w.CustomerPrice,
		ProductDate:     w.ProductDate,
		CreatedAt:       w.CreatedAt,
		Salesman:        w.Salesman,
		SalesmanId:      w.SalesmanId,
		CustomerName:    w.CustomerName,
		CustomerSex:     w.CustomerSex,
		CustomerAddr:    w.CustomerAddr,
		WarrantyPeriod:  w.WarrantyPeriod,
		Lng:             w.Lng,
		Lat:             w.Lat,
		StudentUID:      w.StudentUID,
		StudentName:     w.StudentName,
		StudentSex:      w.StudentSex,
		StudentGrade:    w.StudentGrade,
		StudentSchool:   w.StudentSchool,
		StudentBirthday: w.StudentBirthday,
		UpdatedAt:       w.UpdatedAt,
		PurchaseWay:     w.PurchaseWay,
		Assessment:      w.Assessment,
		RealSale:        w.RealSale,
	}
}

func (w *Warranty) ToModelWarranty() *model.Warranty {
	return &model.Warranty{
		ID:              w.Id,
		Type:            w.Type,
		BuyDate:         w.BuyDate,
		EndpointID:      w.Endpoint,
		CustomerPhone:   w.CustomerPhone,
		Barcode:         w.Barcode,
		ExtBarcode:      w.ExtBarcode,
		Status:          &w.Status,
		State:           &w.State,
		ActivatedAtOld:  w.ActivatedAtOld,
		Number:          w.Number,
		Imei:            w.Imei,
		Model:           w.Model,
		ModelID:         w.ModelId,
		CustomerPrice:   w.CustomerPrice,
		ProductDate:     w.ProductDate,
		CreatedAt:       w.CreatedAt,
		Salesman:        w.Salesman,
		SalesmanID:      w.SalesmanId,
		CustomerName:    w.CustomerName,
		CustomerSex:     w.CustomerSex,
		CustomerAddr:    w.CustomerAddr,
		WarrantyPeriod:  w.WarrantyPeriod,
		Lng:             w.Lng,
		Lat:             w.Lat,
		StudentUID:      w.StudentUID,
		StudentName:     w.StudentName,
		StudentSex:      w.StudentSex,
		StudentGrade:    w.StudentGrade,
		StudentSchool:   w.StudentSchool,
		StudentBirthday: w.StudentBirthday,
		UpdatedAt:       w.UpdatedAt,
		PurchaseWay:     w.PurchaseWay,
		Assessment:      &w.Assessment,
		Realsale:        *w.RealSale,
	}
}
