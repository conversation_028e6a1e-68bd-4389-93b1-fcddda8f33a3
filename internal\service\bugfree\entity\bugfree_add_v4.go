package entity

// BugfreeAddV4 问题反馈提交实体
type BugfreeAddV4 struct {
	UserID          string       `json:"user_id"`          // 用户ID
	Model           string       `json:"model"`            // 机型
	ProductID       string       `json:"product_id"`       // 产品ID
	Title           string       `json:"title"`            // 标题
	Description     string       `json:"description"`      // 描述
	Username        string       `json:"username"`         // 用户名
	PhoneNumber     string       `json:"phone_number"`     // 手机号
	DeviceID        string       `json:"device_id"`        // 设备ID
	IMEI            string       `json:"imei"`             // IMEI码（手表类机型问题必填）
	MachineIdentity string       `json:"machine_identity"` // 机器标识，S/N、序列号或IMEI中的一个，硬件问题必填
	RepeatStep      string       `json:"repeat_step"`      // 重现步骤，软件问题必填
	ResourceType    string       `json:"resource_type"`    // 资源类型，教材资源问题必填
	ResourceName    string       `json:"resource_name"`    // 资源名称，教材资源问题必填
	Press           string       `json:"press"`            // 出版社，教材资源问题必填
	Grade           string       `json:"grade"`            // 年级，教材资源问题必填
	Course          string       `json:"course"`           // 科目，教材资源问题必填
	ResourceVolume  string       `json:"resource_volume"`  // 上册，教材资源问题必填，可选值'上册'、'下册'、'全册'
	SoftwareType    string       `json:"software_type"`    // 软件问题类型，软件问题必填
	HardwareType    string       `json:"hardware_type"`    // 硬件问题类型，硬件问题必填
	SuggestionType  string       `json:"suggestion_type"`  // 意见建议类型，意见建议必填
	Attachments     []Attachment `json:"attachments"`      // 附件列表
}
