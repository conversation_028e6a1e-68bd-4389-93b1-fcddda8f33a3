package alilog

import (
	"bytes"
	"encoding/json"
	"fmt"
	sls "github.com/aliyun/aliyun-log-go-sdk"
	"github.com/gogo/protobuf/proto"
	"go.uber.org/zap"
	"marketing-app/internal/pkg/config"
	"marketing-app/internal/pkg/log"
	"strings"
	"time"
	"unicode/utf8"
)

var (
	client sls.ClientInterface
	err    error
)

func Init() {
	Endpoint := "cn-shenzhen.log.aliyuncs.com"
	//读取环境变量的配置
	AccessKeyId := config.GetString("alibaba.cloud.access.key.id")
	AccessKeySecret := config.GetString("alibaba.cloud.access.key.secret")
	if AccessKeyId == "" || AccessKeySecret == "" {
		log.Error("阿里云日志配置未设置")
	}
	SecurityToken := ""
	// 创建日志服务Client
	provider := sls.NewStaticCredentialsProvider(AccessKeyId, AccessKeySecret, SecurityToken)
	client = sls.CreateNormalInterfaceV2(Endpoint, provider)
}

func SendLog(project, logStore string, logData map[string]any) {
	var contents []*sls.LogContent
	for key, value := range logData {
		strValue := safeValueToString(value, 512*1024) // 阿里云日志单值最大1MB
		cleanedValue := limitStringLength(strValue, 512*1024)
		contents = append(contents, &sls.LogContent{
			Key:   proto.String(key),
			Value: proto.String(cleanedValue),
		})
	}

	// 创建完整的日志条目
	logEntry := &sls.Log{
		Time:     proto.Uint32(uint32(time.Now().Unix())),
		Contents: contents,
	}

	// 发送日志到阿里云
	logGroup := &sls.LogGroup{
		Logs:  []*sls.Log{logEntry},
		Topic: proto.String("yx_marketing_app"),
	}

	err := client.PutLogs(project, logStore, logGroup)
	if err != nil {
		log.Error("发送日志失败", zap.Error(err))
	}
}

// 安全转换任意值为字符串，保留最大信息量
func safeValueToString(value interface{}, maxLength int) string {
	if value == nil {
		return "[nil]"
	}

	switch v := value.(type) {
	case string:
		return v // 直接返回原始字符串
	case json.RawMessage:
		return string(v) // 原始JSON数据直接转换
	case []byte:
		if utf8.Valid(v) {
			return string(v) // 有效UTF8直接作为字符串
		}
		return fmt.Sprintf("[binary:0x%x]", v) // 二进制转HEX
	case error:
		return v.Error() // 错误类型
	case fmt.Stringer:
		return v.String() // 实现Stringer接口的类型
	default:
		// 尝试JSON序列化（不转义HTML字符）
		buf := &bytes.Buffer{}
		encoder := json.NewEncoder(buf)
		encoder.SetEscapeHTML(false) // 关键设置：禁用HTML转义

		if err := encoder.Encode(v); err == nil {
			result := buf.String()
			// 移除JSON编码器添加的换行符
			if len(result) > 0 && result[len(result)-1] == '\n' {
				result = result[:len(result)-1]
			}
			return result
		}

		// 最后使用%#v保留类型信息
		return fmt.Sprintf("%#v", v)
	}
}

func limitStringLength(s string, maxLength int) string {
	// 长度限制（阿里云日志单值最大1MB）
	if maxLength > 0 && len(s) > maxLength {
		// 保留前后部分，中间用...省略
		half := maxLength/2 - 3
		if half < 10 {
			half = 10
		}
		return s[:half] + "..." + s[len(s)-half:]
	}

	return s
}

// 字符串安全处理：转义控制字符 + 长度限制
func sanitizeString(s string, maxLength int) string {
	// 转义控制字符和特殊字符
	var b strings.Builder
	for _, r := range s {
		switch {
		case r == '\n':
			b.WriteString("\\n")
		case r == '\r':
			b.WriteString("\\r")
		case r == '\t':
			b.WriteString("\\t")
		case r < 32 || r == 127: // ASCII控制字符
			fmt.Fprintf(&b, "\\x%02x", r)
		case r == '\\':
			b.WriteString("\\\\")
		case r == '"':
			b.WriteString("\\\"")
		default:
			b.WriteRune(r)
		}
	}

	result := b.String()

	// 长度限制（阿里云日志单值最大1MB）
	if maxLength > 0 && len(result) > maxLength {
		// 保留前后部分，中间用...省略
		half := maxLength/2 - 3
		if half < 10 {
			half = 10
		}
		return result[:half] + "..." + result[len(result)-half:]
	}

	return result
}
