package config

import (
	"marketing-app/internal/pkg/config"
	"time"
)

type AppConfig struct {
	Host        string
	AppID       string
	AppSec      string
	HTTPTimeout time.Duration
}

func LoadBindingConfig() *AppConfig {
	return &AppConfig{
		Host:        config.GetString("machine.binding_url"),
		AppID:       config.GetString("machine.app_id"),
		AppSec:      config.GetString("machine.app_sec"),
		HTTPTimeout: 10 * time.Second,
	}
}
