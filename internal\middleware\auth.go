package middleware

import (
	"errors"
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"marketing-app/internal/cache"
	"marketing-app/internal/pkg/db"
	"marketing-app/internal/pkg/redis"
	"marketing-app/internal/repository"
	"marketing-app/internal/service"
	"net/http"
	"strings"
)

const (
	ErrUnauthorized     = "未登录"
	ErrTokenExpired     = "登录已经失效，请重新登录"
	ErrPermissionDenied = "没有权限"
	ErrToken            = "非法token"
	BearerPrefix        = "Bearer "
)

// RespBody response body.统一返回响应格式（不确定要不要调用api包的的先放这里吧）
type RespBody struct {
	// http code
	OK int `json:"ok"`
	// response message
	Message string `json:"msg"`
	// response data
	Data any `json:"data,omitempty"`
}

func AuthToken() gin.HandlerFunc {
	return func(c *gin.Context) {
		systemType := c.GetHeader("x-gate-type")
		if systemType == "" {
			c.JSON(http.StatusBadRequest, &RespBody{
				OK:      0,
				Message: "系统类型不能为空",
			})
			c.Abort()
			return
		}

		token := c.GetHeader("Authorization")

		if token == "" {
			c.JSON(http.StatusBadRequest, &RespBody{
				OK:      0,
				Message: ErrUnauthorized,
			})
			c.Abort()
			return
		}
		tokenString := strings.Replace(token, BearerPrefix, "", 1)

		// 获取应用
		database, _ := db.GetDB()
		redisClient := redis.GetClient()
		appSystemCache := cache.NewAppSystemCache(redisClient)
		appSystemRepo := repository.NewAppSystemRepository(database)
		appSystemService := service.NewAppSystemService(appSystemCache, appSystemRepo)
		systemInfo, err := appSystemService.GetAppSystem(c)
		if err != nil || systemInfo == nil {
			c.JSON(http.StatusInternalServerError, &RespBody{
				OK:      0,
				Message: "系统不存在或已被禁用",
			})
			c.Abort()
			return
		}

		// 调用 ValidateToken 来验证 token
		adminUserService := service.NewAdminUserService(nil, appSystemService, nil)
		claims, err := adminUserService.ValidateToken(tokenString, systemInfo.JwtKey)
		if err != nil {
			if errors.Is(err, jwt.ErrTokenExpired) {
				c.JSON(http.StatusUnauthorized, &RespBody{
					OK:      0,
					Message: ErrTokenExpired,
				})
				c.Abort()
				return
			}
			c.JSON(http.StatusForbidden, &RespBody{
				OK:      0,
				Message: ErrToken,
			})
			c.Abort()
			return
		}
		uid := claims.UserID

		if claims == nil {
			c.JSON(http.StatusForbidden, &RespBody{
				OK:      0,
				Message: ErrToken,
			})
			c.Abort()
			return
		}
		// Token 验证通过，可以继续处理请求
		c.Set("uid", uid)
		c.Set("username", claims.UserName)
		c.Set("name", claims.Name)
		c.Next()
	}
}
