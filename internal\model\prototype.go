package model

import (
	"time"

	"gorm.io/gorm"
)

// Prototype 样机表
type Prototype struct {
	ID           int             `json:"id" gorm:"column:id"`
	ModelID      int             `json:"model_id" gorm:"column:model_id"`
	Model        string          `json:"model" gorm:"column:model"`
	Barcode      string          `json:"barcode" gorm:"column:barcode"`
	Number       string          `json:"number" gorm:"column:number"`
	Imei         string          `json:"imei" gorm:"column:imei"`
	TopAgency    int             `json:"top_agency" gorm:"column:top_agency"`
	SecondAgency int             `json:"second_agency" gorm:"column:second_agency"`
	Endpoint     int             `json:"endpoint" gorm:"column:endpoint"`
	UserID       int             `json:"user_id" gorm:"column:user_id"`
	Type         int             `json:"type" gorm:"column:type"`
	Status       int             `json:"status" gorm:"column:status"`
	TabletStatus int             `json:"tablet_status" gorm:"column:tablet_status"`
	CreatedAt    *time.Time      `json:"created_at" gorm:"column:created_at"`
	UpdatedAt    *time.Time      `json:"updated_at" gorm:"column:updated_at"`
	RemovedAt    *gorm.DeletedAt `json:"removed_at" gorm:"column:removed_at"`
}

func (p *Prototype) TableName() string {
	return "prototype"
}
