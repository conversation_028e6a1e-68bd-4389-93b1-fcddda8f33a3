package model

import (
	"time"
)

// OwnOrder 对应数据库中的 `own_orders` 表 (老带新订单)
type OwnOrder struct {
	ID               uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	EndpointID       uint      `gorm:"column:endpoint_id;not null" json:"endpoint_id"`                                 // 推荐终端
	UserID           uint      `gorm:"column:user_id;not null" json:"user_id"`                                         // 推荐导购
	Purchaser        string    `gorm:"column:purchaser;type:varchar(10);not null" json:"purchaser"`                    // 购买人姓名
	Mobile           string    `gorm:"column:mobile;type:char(11);not null" json:"mobile"`                             // 手机号
	ShippingAddress  string    `gorm:"column:shipping_address;type:varchar(200);not null" json:"shipping_address"`     // 完整收货地址
	ModelID          uint      `gorm:"column:model_id;not null" json:"model_id"`                                       // 对应machine_type的model_id
	Model            string    `gorm:"column:model;type:varchar(20);not null" json:"model"`                            // 购买机型名称
	Price            uint      `gorm:"column:price;not null" json:"price"`                                             // 机器价格 (单位: 分，以避免浮点数精度问题)
	ModelDesc        string    `gorm:"column:model_desc;type:varchar(20);not null" json:"model_desc"`                  // 机型描述
	ModelPcCover     string    `gorm:"column:model_pc_cover;type:varchar(100);not null" json:"model_pc_cover"`         // 机型封面图片
	ModelMobileCover string    `gorm:"column:model_mobile_cover;type:varchar(100);not null" json:"model_mobile_cover"` // 机型封面图片
	WarrantyID       uint      `gorm:"column:warranty_id;not null;default:0" json:"warranty_id"`                       // 保卡id
	CreatedAt        time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`                             // gorm会自动处理
	UpdatedAt        time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`                             // gorm会自动处理
	Remark           string    `gorm:"column:remark;type:varchar(200);not null;default:''" json:"remark"`
	PromotionSource  string    `gorm:"column:promotion_source;type:enum('company','endpoint','pa');not null;default:endpoint" json:"promotion_source"` // 推广来源，pa-家长助手
	ReadablePrice    string    `gorm:"column:readable_price;type:varchar(10);not null;default:''" json:"readable_price"`                               // 文本格式的价格
	Abandoned        uint8     `gorm:"column:abandoned;not null;default:0" json:"abandoned"`                                                           // 是否放弃购买 (0:否, >0:是)
}

func (OwnOrder) TableName() string {
	return "own_orders"
}
