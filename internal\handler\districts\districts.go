package districts

import (
	"github.com/gin-gonic/gin"
	"marketing-app/internal/handler"
	service "marketing-app/internal/service/districts"
	"strconv"
)

type DistrictHandler interface {
	GetDistricts(C *gin.Context)
}

type Districts struct {
	districtsSvc service.DistrictsService
}

func NewDistrictsHandler(districtsSvc service.DistrictsService) DistrictHandler {
	return &Districts{
		districtsSvc: districtsSvc,
	}
}

func (d *Districts) GetDistricts(c *gin.Context) {
	var (
		err  error
		resp map[string]interface{}
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}

	}()

	pid := c.Query("pid")
	level, _ := strconv.Atoi(c.Query("level"))
	resp, err = d.districtsSvc.FetchDistricts(c, pid, level)
}
