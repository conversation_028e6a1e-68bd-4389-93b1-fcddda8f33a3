package promotion_convertor

import (
	"marketing-app/internal/router/promotion/promotion_list/client"
	"marketing-app/internal/service/promotion/promotion_list/entity"
)

type PromotionListConvertor struct{}

func NewPromotionListConvertor() *PromotionListConvertor {
	return &PromotionListConvertor{}
}

// ClientToEntity 将客户端请求转换为实体
func (c *PromotionListConvertor) ClientToEntity(req *client.PromotionListsRequest) *entity.PromotionListsReq {
	return &entity.PromotionListsReq{
		Id:       req.Id,
		Agency:   req.Agency,
		Endpoint: req.Endpoint,
		Receipt:  req.Receipt,
		ModelId:  req.ModelId,
		Keyword:  req.Keyword,
		Page:     req.Page,
		PageSize: req.PageSize,
	}
}

type PromotionListDetailConvertor struct{}

func NewPromotionListDetailConvertor() *PromotionListDetailConvertor {
	return &PromotionListDetailConvertor{}
}

// ClientToEntity 将客户端详情请求转换为实体
func (c *PromotionListDetailConvertor) ClientToEntity(req *client.PromotionListDetailRequest) *entity.PromotionListDetail {
	return &entity.PromotionListDetail{
		Id: req.Id,
	}
}

type ReceiptUploadConvertor struct{}

func NewReceiptUploadConvertor() *ReceiptUploadConvertor {
	return &ReceiptUploadConvertor{}
}

// ClientToEntity 将客户端上传回执请求转换为实体
func (c *ReceiptUploadConvertor) ClientToEntity(req *client.ReceiptUploadRequest, endpoint int) *entity.ReceiptUpload {
	return &entity.ReceiptUpload{
		Id:       req.Id,
		Endpoint: endpoint,
		Receipt:  req.Receipt,
	}
}
