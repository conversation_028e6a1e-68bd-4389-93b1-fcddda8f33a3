package model

import "time"

// PrototypeUsers 样机演示账号管理表
type PrototypeUsers struct {
	ID           int       `gorm:"column:id;primaryKey;autoIncrement;type:int(10) unsigned" json:"id"`
	Username     *string   `gorm:"column:username;type:varchar(190);comment:用户名" json:"username"`
	Endpoint     *int      `gorm:"column:endpoint;type:int(11);comment:终端id" json:"endpoint"`
	TopAgency    *int      `gorm:"column:top_agency;type:int(11);comment:总代id" json:"top_agency"`
	SecondAgency *int      `gorm:"column:second_agency;type:int(11);comment:二代id" json:"second_agency"`
	Phone        string    `gorm:"column:phone;type:varchar(100);comment:电话号码" json:"phone"`
	CreatedAt    time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt    time.Time `gorm:"column:updated_at" json:"updated_at"`
}

// TableName 指定表名
func (PrototypeUsers) TableName() string {
	return "prototype_users"
}
