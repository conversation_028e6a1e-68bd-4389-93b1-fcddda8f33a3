package returns

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"marketing-app/internal/model"
	"marketing-app/internal/pkg/log"
	"marketing-app/internal/repository/warranty/returns/builder"
	"marketing-app/internal/repository/warranty/returns/dto"
)

type Warranty interface {
	Create(c *gin.Context, cond *builder.Warranty, create *model.Warranty) error
	Get(c *gin.Context, query *builder.Warranty) (data *dto.Warranty, err error)
	Count(c *gin.Context, query *builder.Warranty) (total int64, err error)
	Return(c *gin.Context, cond *builder.Warranty, create *model.WarrantyReturn, upd map[string]interface{}) (err error)
}

type warranty struct {
	db *gorm.DB
}

func NewWarranty(db *gorm.DB) Warranty {
	return &warranty{
		db: db,
	}
}

func (w *warranty) UseTransaction(tx *gorm.DB) *gorm.DB {
	if tx == nil {
		return w.db
	}
	return tx
}

func (w *warranty) Create(c *gin.Context, cond *builder.Warranty, create *model.Warranty) error {
	return cond.Fill(w.db.WithContext(c).Model(&model.Warranty{})).Create(create).Error
}

func (w *warranty) Get(c *gin.Context, query *builder.Warranty) (data *dto.Warranty, err error) {
	err = query.Fill(w.db.WithContext(c).Model(&model.Warranty{})).First(&data).Error
	return
}

func (w *warranty) Count(c *gin.Context, query *builder.Warranty) (total int64, err error) {
	err = query.Fill(w.db.WithContext(c).Model(&model.Warranty{})).Count(&total).Error
	return
}

func (w *warranty) Return(c *gin.Context, cond *builder.Warranty, create *model.WarrantyReturn, upd map[string]interface{}) error {
	return w.db.Transaction(func(tx *gorm.DB) (err error) {
		defer func() {
			if err != nil {
				log.Warn("return warranty transaction err", zap.Error(err))
			}
		}()

		return w.doReturn(c, tx, cond, create, upd)
	})
}

func (w *warranty) doReturn(c *gin.Context, tx *gorm.DB, cond *builder.Warranty, create *model.WarrantyReturn, upd map[string]interface{}) (err error) {
	err = cond.Fill(w.UseTransaction(tx).WithContext(c).Model(&model.Warranty{})).Updates(upd).Error
	if err != nil {
		return
	}
	return w.UseTransaction(tx).WithContext(c).Model(&model.WarrantyReturn{}).Create(create).Error
}
