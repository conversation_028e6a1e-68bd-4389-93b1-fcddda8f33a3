package machine

import (
	"errors"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"marketing-app/internal/consts"
	"marketing-app/internal/model"
	"marketing-app/internal/pkg/log"
	drpMachineBuilder "marketing-app/internal/repository/machine/drp_machine/builder"
	"marketing-app/internal/repository/machine/drp_machine/dto"
	machineTypeBuilder "marketing-app/internal/repository/machine/machine_type/builder"
	machineTypeDto "marketing-app/internal/repository/machine/machine_type/dto"
	"time"
)

type Machine interface {
	GetMachineType(c *gin.Context, query *machineTypeBuilder.MachineType) (data *machineTypeDto.MachineType, err error)
	DrpMachine(c *gin.Context, query *drpMachineBuilder.DrpMachine) (err error)
}

type machine struct {
	db *gorm.DB
}

func NewMachine(db *gorm.DB) Machine {
	return &machine{
		db: db,
	}
}

func (m *machine) UseTransaction(tx *gorm.DB) *gorm.DB {
	if tx == nil {
		return m.db
	}
	return tx
}

func (m *machine) GetMachineType(c *gin.Context, query *machineTypeBuilder.MachineType) (data *machineTypeDto.MachineType, err error) {
	err = query.Fill(m.db.WithContext(c).Model(&model.MachineType{})).First(&data).Error
	return
}

func (m *machine) DrpMachine(c *gin.Context, query *drpMachineBuilder.DrpMachine) (err error) {
	return m.db.Transaction(func(tx *gorm.DB) (err error) {
		defer func() {
			if err != nil {
				log.Warn("drop machine transaction err", zap.Error(err))
			}
		}()

		err = m.drop(tx, query)
		if err != nil {
			err = errors.New("drop machine err")
		}
		return err
	})
}

func (m *machine) drop(tx *gorm.DB, query *drpMachineBuilder.DrpMachine) (err error) {
	now := time.Now()

	drpMachine, err := findDrpMachine(tx, query.Barcode)
	if err != nil {
		return err
	}
	var recordID int
	if drpMachine.ID != 0 {
		recordID, err = findRecordID(tx, query.Barcode)
		if err != nil {
			return err
		}
	}

	var newMachine *model.DrpMachine
	var newRecordID int
	if query.NewBarcode != "" {
		newMachine, err = findDrpMachine(tx, query.NewBarcode)
		if err != nil {
			return err
		}
		if newMachine.ID != 0 {
			newRecordID, err = findRecordID(tx, query.NewBarcode)
			if err != nil {
				return err
			}
		}

	}

	drpStatus, err := handleMachineStatus(
		tx,
		query.Barcode,
		drpMachine,
		query.NewBarcode,
		newMachine,
		query.OperationType,
		query.Return,
		query.Warranty,
		now,
	)
	if err != nil {
		return err
	}

	if recordID != 0 {
		if err = updateRecordDetail(tx, recordID, query.Barcode, drpStatus); err != nil {
			return err
		}
	}

	if newRecordID != 0 {
		if err = updateRecordDetail(tx, newRecordID, query.NewBarcode, 1); err != nil { // 1 is drpNewStatus
			return err
		}
	}

	return nil
}

// findDrpMachine 查询 drp_machines 表
func findDrpMachine(tx *gorm.DB, barcode string) (*model.DrpMachine, error) {
	var m model.DrpMachine
	err := tx.Where("barcode = ? AND status NOT IN (?, ?)", barcode, consts.MachineStatusInactive, consts.MachineStatusOutOfStock).Find(&m).Error
	return &m, err
}

// findRecordID 查询 drp_records_detail 表，查找盘点异常记录ID
func findRecordID(tx *gorm.DB, barcode string) (int, error) {
	if barcode == "" {
		return 0, nil
	}

	type MachineRecord struct {
		ID      int
		Barcode string
	}
	var recordDetail MachineRecord
	err := tx.Table("drp_records_detail as drd").
		Select("dr.id, drd.barcode").
		Joins("RIGHT JOIN drp_machines as dm ON drd.barcode = dm.barcode").
		Joins("RIGHT JOIN drp_records as dr ON dr.id = drd.operate_id").
		Where("dm.barcode = ? AND drd.type != ? AND drd.new_status = ? AND dr.record_type = ?", barcode, 0, -1, 3).
		Scan(&recordDetail).Error

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return 0, err
	}

	if recordDetail.ID != 0 && recordDetail.Barcode != "" {
		return recordDetail.ID, nil
	}

	return 0, nil
}

// updateMachineStatus 更新 drp_machines 表的状态
func updateMachineStatus(tx *gorm.DB, barcode string, status int, subStatus int, now time.Time) error {
	updates := map[string]interface{}{
		"status":     status,
		"sub_status": subStatus,
		"updated_at": now,
	}
	err := tx.Model(&model.DrpMachine{}).Where("barcode = ?", barcode).Updates(updates).Error
	return err
}

// handleMachineStatus 处理机器状态变更
func handleMachineStatus(tx *gorm.DB, barcode string, machine *model.DrpMachine, newBarcode string, newMachine *model.DrpMachine, oType int, ret int, warranty *dto.Warranty, now time.Time) (int, error) {
	drpStatus := 0
	var err error

	switch oType {
	case 1: // 录保卡
		if machine != nil && (machine.Status == 1 || machine.Status == 4 || machine.Status == 5 || machine.Status == 6) {
			err = updateMachineStatus(tx, barcode, 3, 6, now)
			drpStatus = 3
		}
	case 2, 3: // 换机（换机样机）, 退机（退机样机）
		if machine != nil && machine.Status == 3 {
			newStatus := 1
			if ret != 3 {
				newStatus = 5 // or 6 depending on oType
				if oType == 3 {
					newStatus = 6
				}
			}
			err = updateMachineStatus(tx, barcode, newStatus, 5, now)
			drpStatus = newStatus
		}

		if newMachine != nil && (newMachine.Status == 1 || newMachine.Status == 4 || newMachine.Status == 5 || newMachine.Status == 6) {
			err = updateMachineStatus(tx, newBarcode, 3, 6, now)
		}

		if warranty != nil {
			err = handleWarehouse(tx, barcode, warranty, ret, now)
		}

	case 4: // 4--入样机
		if machine != nil && machine.Status == 1 {
			err = updateMachineStatus(tx, barcode, 4, 5, now)
			drpStatus = 4
		}
	default: // 5--样机正常离库
		if machine != nil && (machine.Status == 4 || machine.Status == 5 || machine.Status == 6) {
			err = updateMachineStatus(tx, barcode, 1, 1, now)
			drpStatus = 1
		}
	}

	return drpStatus, err
}

// handleWarehouse 处理仓库逻辑
func handleWarehouse(tx *gorm.DB, barcode string, warranty *dto.Warranty, ret int, now time.Time) error {
	var warehouse model.DrpWarehouse
	if err := tx.Where("endpoint = ?", warranty.Endpoint).First(&warehouse).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil
		}
		return err
	}

	var drpInfo model.DrpMachine
	err := tx.Table("drp_machines").
		Select("drp_machines.status, drp_machines.warehouse_id, drp_machines.id").
		Joins("left join drp_warehouse on drp_machines.warehouse_id = drp_warehouse.id").
		Where("drp_machines.barcode= ? and drp_warehouse.type = ?", barcode, "endpoint").
		Scan(&drpInfo).Error
	if err != nil {
		return err
	}

	if drpInfo.Status == 0 || drpInfo.Status == 2 {
		newStatus := 1
		if ret != 3 {
			newStatus = 5
		}
		updates := map[string]interface{}{
			"warehouse_id": warehouse.ID,
			"status":       newStatus,
			"sub_status":   5,
			"created_at":   now,
			"updated_at":   nil,
		}
		err = tx.Model(&model.DrpMachine{}).Where("barcode = ? AND (status = ? OR status = ?) AND id = ?", barcode, 2, 0, drpInfo.ID).Updates(updates).Error
	}
	return err
}

// updateRecordDetail 更新 drp_records_detail 表
func updateRecordDetail(tx *gorm.DB, recordID int, barcode string, drpStatus int) error {
	err := tx.Model(&model.DrpRecordsDetail{}).Where("operate_id = ? AND barcode = ?", recordID, barcode).Update("new_status", drpStatus).Error
	if err != nil {
		return err
	}

	var count int64
	err = tx.Model(&model.DrpRecordsDetail{}).Where("operate_id = ? AND `type` != ? AND new_status = ?", recordID, 0, -1).Count(&count).Error
	if err != nil {
		return err
	}

	if count == 0 {
		err = tx.Model(&model.DrpRecords{}).Where("id = ?", recordID).Update("status", 1).Error
		if err != nil {
			return err
		}
	}
	return nil
}
