package warranty_entry

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"marketing-app/internal/cache"
	"marketing-app/internal/consts"
	"marketing-app/internal/model"
	"marketing-app/internal/pkg/log"
	"marketing-app/internal/pkg/utils"
	commonRepo "marketing-app/internal/repository/common"
	commonBuilder "marketing-app/internal/repository/common/builder"
	contactRepo "marketing-app/internal/repository/contact"
	contactBuilder "marketing-app/internal/repository/contact/builder"
	endpointRepo "marketing-app/internal/repository/endpoint/endpoint"
	userEndpointRepo "marketing-app/internal/repository/endpoint/user_endpoint"
	userEndpointDto "marketing-app/internal/repository/endpoint/user_endpoint/dto"
	machineRepo "marketing-app/internal/repository/machine"
	drpMachineBuilder "marketing-app/internal/repository/machine/drp_machine/builder"
	machineTypeBuilder "marketing-app/internal/repository/machine/machine_type/builder"
	machineTypeDto "marketing-app/internal/repository/machine/machine_type/dto"
	prototypeRepo "marketing-app/internal/repository/prototype"
	prototypeBuilder "marketing-app/internal/repository/prototype/builder"
	entryRepo "marketing-app/internal/repository/warranty/entry"
	"marketing-app/internal/repository/warranty/entry/builder"
	"marketing-app/internal/repository/warranty/entry/dto"
	entityEntry "marketing-app/internal/service/warranty/warranty_entry/entity"
	"strings"
	"time"
)

type WarrantyEntryService interface {
	CreateWarranty(c *gin.Context, param *entityEntry.Warranty) (data *dto.CreateWarranty, err error)
	CheckWarranty(c *gin.Context, barcode string) (map[string]interface{}, error)
}

type warrantyEntry struct {
	repo             entryRepo.Warranty
	userEndpointRepo userEndpointRepo.UserEndpoint
	prototypeRepo    prototypeRepo.Prototype
	prototypeCache   cache.PrototypeCache
	machineRepo      machineRepo.Machine
	commonRepo       commonRepo.MesDevice
	endpointRepo     endpointRepo.Endpoint
	contactRepo      contactRepo.Contact
}

func NewWarrantyEntry(
	repo entryRepo.Warranty,
	userEndpointRepo userEndpointRepo.UserEndpoint,
	prototypeRepo prototypeRepo.Prototype,
	machineRepo machineRepo.Machine,
	commonRepo commonRepo.MesDevice,
	endpointRepo endpointRepo.Endpoint,
	contactRepo contactRepo.Contact,
	prototypeCache cache.PrototypeCache,
) WarrantyEntryService {
	return &warrantyEntry{
		repo:             repo,
		userEndpointRepo: userEndpointRepo,
		prototypeRepo:    prototypeRepo,
		machineRepo:      machineRepo,
		commonRepo:       commonRepo,
		endpointRepo:     endpointRepo,
		contactRepo:      contactRepo,
		prototypeCache:   prototypeCache,
	}
}

func (w *warrantyEntry) CreateWarranty(c *gin.Context, pending *entityEntry.Warranty) (data *dto.CreateWarranty, err error) {
	err = w.checkPendingWarranty(pending)
	if err != nil {
		return
	}
	endpoint, err := w.checkEndpointInfo(c, pending)
	if err != nil {
		return
	}
	err = w.checkPrototypeEntry(c, pending)
	if err != nil {
		return
	}
	resp, err := w.processWarrantyEntry(c, pending, endpoint)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func (w *warrantyEntry) CheckWarranty(c *gin.Context, barcode string) (map[string]interface{}, error) {
	machine, err := utils.CheckMachine(utils.CheckMachineParams{
		Barcode: barcode,
	})
	return machine, err
}

func (w *warrantyEntry) checkPendingWarranty(warranty *entityEntry.Warranty) (err error) {
	now := warranty.CreatedAt
	buy := warranty.BuyDate
	if buy.After(time.Now()) {
		return errors.New("购机时间不能超过当前时间")
	}
	if buy.AddDate(2, 0, 0).Before(time.Now()) {
		return errors.New("购机时间不能超过两年")
	}
	if !utils.IsBarcode(warranty.Barcode) {
		return errors.New("条码格式错误")
	}
	if !utils.IsPhone(warranty.CustomerPhone) {
		return errors.New("顾客电话格式错误")
	}
	if warranty.StudentGrade != "" && !utils.IsGradeValid(warranty.StudentGrade) {
		return errors.New("学生年级错误")
	}
	if !warranty.StudentBirthday.IsZero() && warranty.StudentBirthday.After(*now) {
		return errors.New("学生生日不能超过当前时间")
	}

	return
}

func (w *warrantyEntry) checkEndpointInfo(c *gin.Context, param *entityEntry.Warranty) (endpoint *userEndpointDto.EndpointAgency, err error) {
	endpoint, err = w.userEndpointRepo.GetEndpointAgencyByUid(c, param.Uid)
	if endpoint == nil {
		err = errors.New("账号无权限添加电子保卡")
		return nil, err
	}
	return
}

func (w *warrantyEntry) checkPrototypeEntry(c *gin.Context, pending *entityEntry.Warranty) (err error) {
	prototypeInfo, err := w.prototypeRepo.GetPrototypeMachineTypeConfig(
		c,
		prototypeBuilder.NewPrototype().
			JoinPrototypeConfigEq("LEFT JOIN prototype_config pc ON prototype.model_id = pc.model_id").
			JoinMachineTypeEq("LEFT JOIN machine_type mt ON prototype.model_id = mt.model_id").
			BarcodeEq(pending.Barcode). // 判断是否是演示样机
			StatusIn(consts.WarrantyStatusActive),
	)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if prototypeInfo.Type == consts.PrototypeTypeDemo && prototypeInfo.Discontinued == consts.PrototypeListingOn {
		return errors.New("演示样机在库不允许录入保卡")
	}
	return nil
}

func (w *warrantyEntry) processWarrantyEntry(
	c *gin.Context,
	pending *entityEntry.Warranty,
	endpoint *userEndpointDto.EndpointAgency,
) (resp *dto.CreateWarranty, err error) {
	// 保卡查询
	var existingWarranty *dto.WarrantyMachineType
	existingWarranty, err = w.repo.GetWarrantyMachineType(
		c,
		builder.NewWarranty().
			JoinMachineType("LEFT JOIN machine_type mt ON warranty.model_id = mt.model_id").
			BarcodeOrExtBarcodeEq(pending.Barcode, pending.Barcode).
			StatusIn(consts.WarrantyStatusActive, consts.WarrantyStatusVirtual),
	)
	if err != nil {
		err = errors.Wrap(err, "查找保卡失败")
		return
	}
	if existingWarranty != nil && existingWarranty.Status == consts.WarrantyStatusActive {
		err = errors.New("电子保卡已存在")
		return
	}

	var finalWarranty *model.Warranty
	if existingWarranty != nil && existingWarranty.ID != 0 {
		// 处理旧保卡并更新
		finalWarranty, resp, err = w.processExistingWarranty(c, pending, endpoint, existingWarranty)
		if err != nil {
			return
		}
		err = w.repo.Update(
			c,
			finalWarranty,
			builder.NewWarranty().
				BarcodeEq(finalWarranty.Barcode).
				StatusIn(consts.WarrantyStatusVirtual).
				DoOmit("barcode", "model"),
		)
	} else {
		// 处理新保卡并添加
		finalWarranty, resp, err = w.processNewWarranty(c, pending, endpoint)
		if err != nil {
			return
		}
		err = w.repo.Create(c, finalWarranty)
	}
	if err != nil {
		err = errors.Wrap(err, "保存保卡信息失败")
		return
	}
	// 激活终端
	if err = w.endpointRepo.Activate(c, endpoint.ID); err != nil {
		log.Warn(fmt.Sprintf("激活终端[%d]失败", endpoint.ID), zap.Error(err))
	}
	// 补充响应信息
	resp.Id = finalWarranty.ID
	resp.EndpointName = endpoint.Name
	resp.EndpointAddress = endpoint.Address
	resp.EndpointPhone = endpoint.Phone
	resp.EndpointPhone = endpoint.Manager
	// TODO: 通知成功添加 notifyAddSuccess
	// 同步进销存
	err = w.machineRepo.DrpMachine(
		c,
		drpMachineBuilder.NewDrpMachine().
			BarcodeEq(finalWarranty.Barcode).
			StatusOmit(consts.MachineStatusInactive, consts.MachineStatusOutOfStock).
			OperationTypeEq(1).
			ReturnEq(0),
	)
	if err != nil {
		return nil, err
	}
	// 同步联系人信息
	if pending.Contact == 1 {
		err = w.contactRepo.Load(c, contactBuilder.NewContact().WarrantyEq(finalWarranty))
		if err != nil {
			err = errors.Wrap(err, "导入联系人失败")
			return nil, err
		}
	}
	// 取消样机
	err = w.cancelPrototype(
		c,
		prototypeBuilder.NewPrototype().
			JoinMachineTypeEq("LEFT JOIN machine_type mt ON prototype.model_id = mt.model_id").
			BarcodeEq(finalWarranty.Barcode).
			NumberEq(finalWarranty.Number).
			StatusIn(consts.PrototypeStatusInStock),
	)
	if err != nil {
		err = errors.Wrap(err, "取消样机失败")
		return nil, err
	}
	// 更新老带新订单保卡id字段
	err = w.repo.UpdateOldWithNewOrder(
		c,
		builder.NewOwnOrder().ModelEq(finalWarranty.Model).EndpointIdEq(finalWarranty.ID).MobileEq(finalWarranty.CustomerPhone).WarrantyIdEq(0).DoOrderBy("created_at DESC"),
		builder.NewWarranty().BarcodeEq(finalWarranty.Barcode).StatusIn(consts.WarrantyStatusActive),
	)
	if err != nil {
		log.Error("更新老带新订单失败", zap.Error(err))
	}
	// TODO: 补录保卡(需要前端传来is_supply字段,新版暂时没有)
	return resp, nil
}

func (w *warrantyEntry) processExistingWarranty(
	c *gin.Context,
	pending *entityEntry.Warranty,
	endpoint *userEndpointDto.EndpointAgency,
	existingWarranty *dto.WarrantyMachineType,
) (updateWarranty *model.Warranty, resp *dto.CreateWarranty, err error) {
	// 校验已有保卡状态
	err = w.validateWarranty(c, existingWarranty, endpoint, pending)
	if err != nil {
		return
	}
	// 获取机器信息
	machineType, err := w.getMachineType(c, existingWarranty.ModelID)
	if err != nil {
		return
	}
	// 是否纳入考核
	assessment, err := w.determineWarrantyAssessment(c, pending, existingWarranty)
	if err != nil {
		return
	}

	now := time.Now()
	createdAtNew := time.Time{}
	status := consts.WarrantyStatusActive
	updateWarranty = &model.Warranty{
		Barcode:             pending.Barcode,
		Imei:                existingWarranty.Imei,
		ExtBarcode:          pending.ExtBarcode,
		EndpointID:          endpoint.ID,
		BuyDate:             pending.BuyDate,
		Assessment:          &assessment,
		SalesmanID:          pending.SalesmanID,
		Salesman:            pending.Salesman,
		CustomerPrice:       machineType.CustomerPrice,
		CustomerName:        pending.CustomerName,
		CustomerSex:         pending.CustomerSex,
		CustomerPhone:       pending.CustomerPhone,
		CustomerAddr:        pending.CustomerAddr,
		StudentUID:          pending.StudentUID,
		CreatedAt:           pending.CreatedAt,
		CreatedAtNew:        &createdAtNew,
		Status:              &status,
		UpdatedAt:           &now,
		StudentName:         pending.StudentName,
		StudentSex:          pending.StudentSex,
		StudentSchoolID:     pending.StudentSchoolID,
		StudentSchool:       pending.StudentSchool,
		StudentSchoolAdcode: pending.StudentSchoolAdcode,
		StudentGrade:        pending.StudentGrade,
		StudentBirthday:     pending.StudentBirthday,
		PurchaseWay:         pending.PurchaseWay,
		Recommender:         pending.Recommender,
		RecommenderPhone:    pending.RecommenderPhone,
		Type:                pending.Type,
		Lng:                 pending.Lng,
		Lat:                 pending.Lat,
	}

	resp = &dto.CreateWarranty{
		Barcode:         pending.Barcode,
		ExtBarcode:      pending.ExtBarcode,
		Salesman:        pending.Salesman,
		CustomerName:    pending.CustomerName,
		CustomerPhone:   pending.CustomerPhone,
		CustomerAddress: pending.CustomerAddr,
		Model:           pending.Model,
		Endpoint:        endpoint.ID,
		BuyDate:         pending.BuyDate.Format(time.DateOnly),
		StudentName:     pending.StudentName,
		StudentSchool:   pending.StudentSchool,
		StudentGrade:    pending.StudentGrade,
		StudentBirthday: pending.StudentBirthday.Format(time.DateOnly),
		CreatedAt:       pending.CreatedAt.Format(time.DateTime),
		CustomerPrice:   pending.CustomerPrice,
		CustomerSex:     pending.CustomerSex,
		StudentSex:      pending.StudentSex,
		SalesmanId:      pending.SalesmanID,
	}

	return updateWarranty, resp, nil
}

func (w *warrantyEntry) processNewWarranty(
	c *gin.Context,
	pending *entityEntry.Warranty,
	endpoint *userEndpointDto.EndpointAgency,
) (addWarranty *model.Warranty, resp *dto.CreateWarranty, err error) {
	// 查询MES设备信息
	mesDevices, err := w.commonRepo.GetMesDevices(
		c,
		commonBuilder.NewMesDevices().
			BarcodeEq(pending.Barcode).
			StateIn(consts.WarrantyMesDevicesStateDefault, consts.WarrantyMesDevicesStateNormal, consts.WarrantyMesDevicesStateReturn, consts.WarrantyMesDevicesStateRenew).
			DoOrderBy("mes_devices.update_time DESC"),
	)
	if err != nil || mesDevices == nil {
		err = errors.New("无效的条形码")
		return
	}
	// 获取机器信息
	machineType, err := w.getMachineType(c, mesDevices.ModelId)
	if err != nil {
		return
	}
	// 校验特定品类的学生信息
	if machineType.CategoryId == consts.ModelCategoryAIDictionaryPen && endpoint.Channel == "agency" && strings.HasPrefix(machineType.Name, "D") {
		err = w.validateStudentInfo(pending)
		if err != nil {
			return
		}
	}
	// 校验同一电话录入数量
	if machineType.CategoryId == consts.ModelCategoryStudentPad {
		err = w.checkEntryPhone(c, machineType.CategoryId, pending.CustomerPhone)
		if err != nil {
			return
		}
	}
	// 是否纳入考核
	assessment := 1
	if machineType.CategoryId == consts.ModelCategoryStudentPad {
		assessment = 0
	}

	addWarranty = &model.Warranty{
		Imei:                mesDevices.Imei1,
		Barcode:             mesDevices.Barcode,
		ExtBarcode:          pending.ExtBarcode,
		Salesman:            pending.Salesman,
		CustomerName:        pending.CustomerName,
		CustomerPhone:       pending.CustomerPhone,
		CustomerAddr:        pending.CustomerAddr,
		ModelID:             mesDevices.ModelId,
		Model:               mesDevices.Model,
		EndpointID:          endpoint.ID,
		BuyDate:             pending.BuyDate,
		ProductDate:         mesDevices.ProductDate,
		Lng:                 pending.Lng,
		Lat:                 pending.Lat,
		StudentName:         pending.StudentName,
		StudentSchoolAdcode: pending.StudentSchoolAdcode,
		StudentSchoolID:     pending.StudentSchoolID,
		StudentSex:          pending.StudentSex,
		StudentSchool:       pending.StudentSchool,
		StudentGrade:        pending.StudentGrade,
		StudentBirthday:     pending.StudentBirthday,
		CustomerPrice:       machineType.CustomerPrice,
		SalesmanID:          pending.SalesmanID,
		State:               &mesDevices.Status,
		Number:              mesDevices.Number,
		CustomerSex:         pending.CustomerSex,
		PurchaseWay:         pending.PurchaseWay,
		CreatedAt:           pending.CreatedAt,
		Recommender:         pending.Recommender,
		RecommenderPhone:    pending.RecommenderPhone,
		Assessment:          &assessment,
		StudentUID:          pending.StudentUID,
		Type:                pending.Type,
	}

	resp = &dto.CreateWarranty{
		Barcode:         mesDevices.Barcode,
		ExtBarcode:      pending.ExtBarcode,
		Number:          mesDevices.Number,
		Salesman:        pending.Salesman,
		CustomerName:    pending.CustomerName,
		CustomerPhone:   pending.CustomerPhone,
		CustomerAddress: pending.CustomerAddr,
		ModelId:         mesDevices.ModelId,
		Model:           mesDevices.Model,
		Endpoint:        endpoint.ID,
		BuyDate:         pending.BuyDate.Format(time.DateOnly),
		ProductDate:     mesDevices.ProductDate.Format(time.DateOnly),
		StudentName:     pending.StudentName,
		StudentSchool:   pending.StudentSchool,
		StudentGrade:    pending.StudentGrade,
		StudentSex:      pending.StudentSex,
		StudentBirthday: pending.StudentBirthday.Format(time.DateOnly),
		CreatedAt:       pending.CreatedAt.Format(time.DateTime),
		CustomerPrice:   machineType.CustomerPrice,
		CustomerSex:     pending.CustomerSex,
		SalesmanId:      pending.SalesmanID,
	}

	return addWarranty, resp, nil
}

func (w *warrantyEntry) determineWarrantyAssessment(c *gin.Context, pending *entityEntry.Warranty, existingWarranty *dto.WarrantyMachineType) (int, error) {
	assessment := 1
	if existingWarranty.CategoryId == consts.ModelCategoryStudentPad {
		// 检查是否在样机库
		prototype, err := w.prototypeRepo.Get(
			c,
			prototypeBuilder.NewPrototype().
				BarcodeEq(existingWarranty.Barcode).
				StatusIn(consts.WarrantyStatusActive),
		)
		if err != nil {
			err = errors.Wrap(err, "校验样机库失败")
			return 0, err
		}
		// 判断激活时间是否合法
		if prototype == nil && !existingWarranty.ActivatedAtOld.IsZero() && pending.BuyDate.After(*existingWarranty.ActivatedAtOld) {
			isDateRangeValid := existingWarranty.ActivatedAtOld.After(time.Date(2021, 1, 31, 0, 0, 0, 0, time.UTC)) ||
				existingWarranty.ActivatedAtOld.Before(time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC))
			if isDateRangeValid {
				err = fmt.Errorf("录入失败，购买日期不得晚于机器的激活日期。 \n本机的激活时间为%s，请修改购买日期后重新提交。",
					existingWarranty.ActivatedAtOld.Format("2006-01-02"))
				return 0, err
			}
		}
		// 确定是否纳入考核
		if existingWarranty.ActivatedAtOld.IsZero() {
			assessment = 0 // 没有激活时间按正常流程
		} else {
			now := time.Now()
			if existingWarranty.ActivatedAtOld.After(time.Date(2021, 1, 31, 0, 0, 0, 0, time.UTC)) {
				// 录入时间和购买时间同年同月并且纳入考核
				if pending.BuyDate.Format("2006-01") == now.Format("2006-01") ||
					(pending.BuyDate.AddDate(0, 1, 0).Format("2006-01") == now.Format("2006-01") && now.Day() <= 3) {
					assessment = 1
				}
			}
		}
	}
	return assessment, nil
}

func (w *warrantyEntry) getMachineType(c *gin.Context, modelId int) (*machineTypeDto.MachineType, error) {
	machineType, err := w.machineRepo.GetMachineType(
		c,
		machineTypeBuilder.NewMachineType().ModelIdEq(modelId),
	)
	if err != nil {
		// TODO: 微信通知失败
		err = errors.Wrap(err, "未找到机型信息")
		return nil, err
	}
	return machineType, nil
}

func (w *warrantyEntry) validateWarranty(
	c *gin.Context,
	existingWarranty *dto.WarrantyMachineType,
	endpoint *userEndpointDto.EndpointAgency,
	pending *entityEntry.Warranty,
) (err error) {
	// 校验是否为副机条码
	if existingWarranty.ExtBarcodeNum < 0 {
		err = errors.New("此条码为副机条码，不可进行保卡录入，请输入对应的主机条码")
		return
	}
	// 校验特定品类的学生信息
	if existingWarranty.CategoryId == consts.ModelCategoryAIDictionaryPen && endpoint.Channel == "agency" && strings.HasPrefix(existingWarranty.Model, "D") {
		err = w.validateStudentInfo(pending)
		if err != nil {
			return
		}
	}
	// 校验同一电话录入数量
	if existingWarranty.CategoryId == consts.ModelCategoryStudentPad {
		return w.checkEntryPhone(c, existingWarranty.CategoryId, pending.CustomerPhone)
	}
	return
}

func (w *warrantyEntry) validateStudentInfo(pending *entityEntry.Warranty) (err error) {
	if pending.StudentName == "" {
		err = errors.New("学生姓名不能为空")
		return
	}
	if pending.StudentSchool == "" {
		err = errors.New("学生学校不能为空")
		return
	}
	if pending.StudentGrade == "" {
		err = errors.New("学生年级不能为空")
		return
	}
	return
}

func (w *warrantyEntry) cancelPrototype(c *gin.Context, query *prototypeBuilder.Prototype) error {
	prototype, err := w.prototypeRepo.GetPrototypeMachineType(c, query)
	if err != nil {
		return err
	}
	if prototype.ID == 0 {
		log.Warn("无对应条码的样机数据")
		return nil
	}
	if prototype.Status != consts.PrototypeStatusInStock {
		err = errors.New("非样机")
		return err
	}
	// 删除样机数据
	affected, err := w.prototypeRepo.Update(
		c,
		prototypeBuilder.NewPrototype().BarcodeEq(query.Barcode).NumberEq(query.Number),
		map[string]interface{}{
			"status":     0,
			"removed_at": time.Now().Format(time.DateTime),
			"updated_at": time.Now().Format(time.DateTime),
		})
	// 更新样机状态
	if err != nil {
		err = errors.Wrap(err, "更新样机状态失败")
		return err
	}
	if affected == 0 {
		log.Warn("未找到样机数据")
		return nil
	}
	// 删除缓存
	err = w.prototypeCache.Del(c, prototype.Number)
	if err != nil {
		err = errors.New("删除缓存失败")
		return err
	}
	if prototype.CategoryID == consts.ModelCategoryStudentPad {
		// TODO:取消绑定（app端）
	}
	return nil
}

func (w *warrantyEntry) checkEntryPhone(c *gin.Context, categoryId int, customerPhone string) (err error) {
	cnt, _ := w.repo.Count(
		c,
		builder.NewWarranty().
			JoinMachineType("RIGHT JOIN machine_type mt ON warranty.model_id = mt.model_id").
			StatusIn(consts.WarrantyStatusActive).
			CustomerPhoneEq(customerPhone).
			RawWhere(gorm.Expr("warranty.buy_date >= DATE_SUB(CURDATE(), INTERVAL 1 YEAR)")).
			CategoryIdEq(categoryId),
	)
	if cnt >= 2 {
		err = errors.Errorf("该品类该手机号码已录入 %d 台电子保卡，同种品类同一手机号码一年内最多可以录入2台产品的电子保卡。若遇团购或大批量采购的请联系公司邱伟雄进行录入。", cnt)
		return
	}
	return
}
