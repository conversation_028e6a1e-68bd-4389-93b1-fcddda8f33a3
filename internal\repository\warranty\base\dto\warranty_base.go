package dto

import "time"

type WarrantyBase struct {
	Id            int    `json:"id"`
	Barcode       string `json:"barcode,omitempty"`
	ExtBarcode    string `json:"extBarcode,omitempty"`
	Model         string `json:"model,omitempty"`
	SalesmanId    int    `json:"salesman_id,omitempty"`
	Salesman      string `json:"salesman,omitempty"`
	BuyDate       string `json:"buy_date,omitempty"`
	CreatedAt     string `json:"createdAt,omitempty"`
	Callback      int    `json:"callback,omitempty"`
	CustomerPhone string `json:"customerPhone,omitempty"`
	StudentName   string `json:"student_name,omitempty"`
}

type WarrantyDetail struct {
	Barcode         string `json:"barcode,omitempty"`
	Number          string `json:"number,omitempty"`
	Imei            string `json:"imei,omitempty"`
	ExtBarcode      string `json:"ext_barcode,omitempty"`
	Salesman        string `json:"salesman,omitempty"`
	CustomerName    string `json:"customer_name,omitempty"`
	CustomerPhone   string `json:"customer_phone,omitempty"`
	CustomerAddr    string `json:"customer_addr,omitempty"`
	StudentName     string `json:"student_name,omitempty"`
	StudentSchool   string `json:"student_school,omitempty"`
	StudentGrade    string `json:"student_grade,omitempty"`
	StudentBirthday string `json:"student_birthday,omitempty"`
	BuyDate         string `json:"buy_date,omitempty"`
	ProductDate     string `json:"product_date,omitempty"`
	ModelId         int    `json:"model_id,omitempty"`
	Model           string `json:"model,omitempty"`
	ActivatedAtOld  string `json:"activated_at,omitempty"`
	PurchaseWay     string `json:"purchase_way,omitempty"`
	CreatedAt       string `json:"created_at,omitempty"`

	EndpointId      int    `json:"endpoint_id,omitempty"`
	EndpointName    string `json:"endpoint_name,omitempty"`
	EndpointAddress string `json:"endpoint_address,omitempty"`
	EndpointPhone   string `json:"endpoint_phone,omitempty"`
	EndpointManager string `json:"endpoint_manager,omitempty"`
}

type WarrantyMachineType struct {
	Id             int        `json:"id"`
	Barcode        string     `json:"barcode"`
	Number         string     `json:"number"`
	Status         int        `json:"status"`
	ActivatedAtOld *time.Time `json:"activated_at_old,omitempty"`
	CategoryId     int        `json:"category_id"`
}
