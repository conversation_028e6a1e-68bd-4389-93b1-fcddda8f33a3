package entry

import (
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"marketing-app/internal/model"
	"marketing-app/internal/pkg/log"
	"marketing-app/internal/repository/warranty/entry/builder"
	"marketing-app/internal/repository/warranty/entry/dto"
)

type Warranty interface {
	Create(c *gin.Context, add *model.Warranty) error
	Get(c *gin.Context, tx *gorm.DB, query *builder.Warranty) (data *dto.GetWarranty, err error)
	Update(c *gin.Context, update *model.Warranty, cond *builder.Warranty) error
	Count(c *gin.Context, cond *builder.Warranty) (total int64, err error)

	UpdateOldWithNewOrder(c *gin.Context, query *builder.OwnOrder, cond *builder.Warranty) error
	GetWarrantyMachineType(c *gin.Context, query *builder.Warranty) (data *dto.WarrantyMachineType, err error)
}

type warranty struct {
	db *gorm.DB
}

func NewWarranty(db *gorm.DB) Warranty {
	return &warranty{
		db: db,
	}
}

func (w *warranty) UseTransaction(tx *gorm.DB) *gorm.DB {
	if tx == nil {
		return w.db
	}
	return tx
}

func (w *warranty) Create(c *gin.Context, add *model.Warranty) error {
	return w.db.WithContext(c).Create(add).Error
}

func (w *warranty) Get(c *gin.Context, tx *gorm.DB, query *builder.Warranty) (data *dto.GetWarranty, err error) {
	err = query.Fill(w.UseTransaction(tx).WithContext(c).Model(&model.Warranty{})).First(&data).Error
	return data, err
}

func (w *warranty) Update(c *gin.Context, update *model.Warranty, cond *builder.Warranty) error {
	return w.db.Transaction(func(tx *gorm.DB) (err error) {
		defer func() {
			if err != nil {
				log.Warn("update warranty transaction err", zap.Error(err))
			}
		}()

		affected, err := w.update(c, tx, update, cond)
		if err != nil || affected == 0 {
			err = errors.New("update warranty err")
		}
		return err
	})
}

func (w *warranty) GetWarrantyMachineType(c *gin.Context, query *builder.Warranty) (data *dto.WarrantyMachineType, err error) {
	err = query.Fill(w.db.WithContext(c).Model(&model.Warranty{}).Select("warranty.*, mt.category_id, mt.ext_barcode_num")).Find(&data).Error
	return data, err
}

func (w *warranty) UpdateOldWithNewOrder(c *gin.Context, query *builder.OwnOrder, cond *builder.Warranty) error {
	return w.db.Transaction(func(tx *gorm.DB) (err error) {
		defer func() {
			if err != nil {
				log.Warn("update old_with_new order transaction err", zap.Error(err))
			}
		}()

		orderToUpdate, err := w.getOrder(
			c,
			tx,
			builder.NewOwnOrder().ModelEq(query.Model).EndpointIdEq(query.EndpointId).
				MobileEq(query.Mobile).WarrantyIdEq(query.WarrantyId).
				DoOrderBy(query.OrderBy).ForUpdate(),
		)
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn("获取待更新订单失败")
			return nil
		}
		validWarranty, err := w.Get(c, tx, builder.NewWarranty().BarcodeEq(cond.Barcode).StatusIn(cond.Status...))
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn("获取有效保卡失败")
			return
		}
		err = w.updateOldWithNewOrder(c, tx, builder.NewOwnOrder().IdEq(orderToUpdate.Id), map[string]interface{}{
			"warranty_id": validWarranty.Id,
		})
		if err != nil {
			err = errors.Wrap(err, "更新老带新订单保卡id失败")
			return
		}

		return nil
	})
}

func (w *warranty) Count(c *gin.Context, cond *builder.Warranty) (cnt int64, err error) {
	err = cond.Fill(w.db.WithContext(c).Model(&model.Warranty{})).Count(&cnt).Error
	return cnt, err
}

func (w *warranty) update(c *gin.Context, tx *gorm.DB, upd *model.Warranty, cond *builder.Warranty) (affected int64, err error) {
	res := cond.Fill(w.UseTransaction(tx).WithContext(c).Model(&model.Warranty{}).Clauses(clause.Returning{})).Updates(upd)
	return res.RowsAffected, res.Error
}

func (w *warranty) getOrder(c *gin.Context, tx *gorm.DB, query *builder.OwnOrder) (data *dto.GetOwnOrder, err error) {
	err = query.Fill(w.UseTransaction(tx).WithContext(c).Model(&model.OwnOrder{})).First(&data).Error
	return data, err
}

func (w *warranty) updateOldWithNewOrder(c *gin.Context, tx *gorm.DB, cond *builder.OwnOrder, upd map[string]interface{}) (err error) {
	err = cond.Fill(w.UseTransaction(tx).WithContext(c).Model(&model.OwnOrder{}).Updates(upd)).Error
	return err
}
