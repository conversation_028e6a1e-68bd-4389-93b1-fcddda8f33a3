package entity

// PromotionListsReq 促销活动列表请求实体
type PromotionListsReq struct {
	Id       int    // 促销活动ID
	Agency   int    // 代理商ID
	Endpoint int    // 终端ID
	Receipt  int    // 回执状态 1-已上传 2-未上传
	ModelId  []int  // 机型ID列表
	Keyword  string // 关键词搜索(条码)
	Page     int    // 页码
	PageSize int    // 每页数量
}

// PromotionListDetail 促销活动详情请求实体
type PromotionListDetail struct {
	Id int // 促销活动列表ID
}

// ReceiptUpload 上传回执请求实体
type ReceiptUpload struct {
	Id       int    // 促销活动列表ID
	Endpoint int    // 终端ID
	Receipt  string // 回执图片，多个用逗号分隔
}
