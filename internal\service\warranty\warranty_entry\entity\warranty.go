package entity

import "time"

type Warranty struct {
	ID                  int
	Uid                 int
	ModelID             int
	Model               string
	Barcode             string
	Number              string
	Imei                string
	ExtBarcode          string
	Source              int
	Endpoint            int
	BuyDate             *time.Time
	WarrantyPeriod      *time.Time
	ProductDate         *time.Time // 出厂日期（mes入库时间）
	CreatedAtNew        *time.Time
	CreatedAt           *time.Time
	ActivatedAtOld      *time.Time
	ActivatedID         int
	Realsale            int // 1-是，0-否
	Assessment          uint
	Status              int // -1-虚卡翻新，0-虚卡，1-正常，2-换机，3-退机，4-其他，5-翻新
	SalesmanID          int
	Salesman            string
	CustomerPrice       float64
	CustomerName        string
	CustomerSex         string // m-男，f-女
	CustomerPhone       string
	CustomerAddr        string
	StudentUID          int
	StudentName         string
	StudentSex          string
	StudentSchoolID     int
	StudentSchool       string
	StudentSchoolAdcode int
	StudentGrade        string
	StudentBirthday     *time.Time
	CallBack            int
	PurchaseWay         string
	Recommender         string
	RecommenderPhone    string
	State               int
	InBillDate          string
	OutBillDate         string
	OutCustCode         string
	Type                int // 1正常，-1-已冻结
	Prototype           int // 0为非样机  1为样机
	UpdatedAt           string
	EcCreatedAt         string
	EcType              int
	EcEndpoint          int
	Channel             string
	ActivatedStatus     int
	ActivatedAt         string
	LngActivated        string
	LatActivated        string
	Lng                 string
	Lat                 string

	Contact int
}
