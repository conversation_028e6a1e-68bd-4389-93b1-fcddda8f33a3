package model

import (
	"time"
)

// BugfreeLabel 问题反馈分类标签
type BugfreeLabel struct {
	Id           int       `json:"id" gorm:"column:id;primaryKey;autoIncrement"`                                                                                                                               // 主键ID
	Name         string    `json:"name" gorm:"column:name;type:varchar(100);comment:名称"`                                                                                                                       // 名称
	Type         int       `json:"type" gorm:"column:type;type:tinyint;default:1;comment:类型：1终端服务反馈标签；2平板反馈标签"`                                                                                                // 类型
	Category     int       `json:"category" gorm:"column:category;type:tinyint unsigned;default:0;comment:分类ID：1教材资源；2软件问题；3硬件问题；4意见建议；5学习桌椅；6竞品功能推荐；7错题反馈"`                                                   // 分类ID
	CategoryKey  string    `json:"category_key" gorm:"column:category_key;type:varchar(100);comment:分类KEY：textbook教材资源；software软件问题；hardware硬件问题；suggestion意见建议；study_desk学习桌椅；competitor竞品功能推荐；question错题反馈"` // 分类KEY
	CategoryName string    `json:"category_name" gorm:"column:category_name;type:varchar(100);comment:分类名称"`                                                                                                   // 分类名称
	Order        int       `json:"order" gorm:"column:order;not null"`                                                                                                                                         // 排序
	Visibility   int       `json:"visibility" gorm:"column:visibility;type:tinyint;default:1;not null"`                                                                                                        // 是否可见
	CreatedAt    time.Time `json:"created_at" gorm:"column:created_at;type:timestamp;default:CURRENT_TIMESTAMP"`                                                                                               // 创建时间
	UpdatedAt    time.Time `json:"updated_at" gorm:"column:updated_at;type:timestamp;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`                                                                   // 更新时间
}

// TableName 指定表名
func (BugfreeLabel) TableName() string {
	return "bugfree_label"
}
