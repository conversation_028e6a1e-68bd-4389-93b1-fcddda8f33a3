package redis

import (
	"context"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"marketing-app/internal/pkg/config"
	"sync"
	"time"
)

var (
	client *redis.Client
	once   sync.Once
)

// Config holds the configuration for connecting to a Redis server.
type Config struct {
	Username string
	Addr     string
	Password string
	DB       int
}

// Init creates a new Redis client using the provided configuration.
func Init(c ...Config) error {
	var err error
	var cfg Config
	if len(c) == 0 {
		if err := config.UnmarshalKey("redis", &cfg); err != nil {
			panic(fmt.Sprintf("解析Redis配置失败: %v", err))
		}
		if gin.Mode() != gin.DebugMode {
			cfg.Addr = config.GetString("redis.addr") //读取环境变量的配置
		}
	} else {
		cfg = c[0]
	}
	once.Do(func() {
		client = redis.NewClient(&redis.Options{
			Username: cfg.Username,
			Addr:     cfg.Addr,
			Password: cfg.Password, // 没有密码，默认值
			DB:       cfg.DB,       // 默认DB 0
		})
	})
	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	if pingErr := client.Ping(ctx).Err(); pingErr != nil {
		err = pingErr
	}
	return err
}

// GetClient 获取Redis客户端实例
func GetClient() *redis.Client {
	if client == nil {
		panic("Redis客户端未初始化，请先调用Init方法")
	}
	return client
}

// Close 关闭Redis连接
func Close() error {
	if client != nil {
		return client.Close()
	}
	return nil
}
