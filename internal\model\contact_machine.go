package model

import "time"

type ContactMachine struct {
	ID         uint       `gorm:"primaryKey;autoIncrement;column:id"`
	EndpointID int        `gorm:"not null;default:0;column:endpoint_id;uniqueIndex:endpoint_id_name;comment:终端id"`
	Name       string     `gorm:"type:varchar(50);not null;default:'';column:name;uniqueIndex:endpoint_id_name;comment:名称"`
	CreatedAt  *time.Time `gorm:"column:created_at"`
	UpdatedAt  *time.Time `gorm:"column:updated_at"`
}

// TableName sets the insert table name for this struct type
func (ContactMachine) TableName() string {
	return "contact_machine"
}
