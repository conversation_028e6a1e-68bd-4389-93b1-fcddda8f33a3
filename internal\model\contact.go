package model

import (
	"time"
)

type Contact struct {
	ID               uint64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	EndpointID       uint       `gorm:"column:endpoint_id;default:0" json:"endpoint_id"`
	CustomerName     string     `gorm:"column:customer_name;type:varchar(50);default:''" json:"customer_name"`
	CustomerPhone    string     `gorm:"column:customer_phone;type:varchar(50);default:'';uniqueIndex:idx_endpoint_customer_phone,priority:2" json:"customer_phone"`
	StudentName      string     `gorm:"column:student_name;type:varchar(50);default:''" json:"student_name"`
	StudentSchool    string     `gorm:"column:student_school;type:varchar(100);default:''" json:"student_school"`
	StudentGrade     string     `gorm:"column:student_grade;type:varchar(100);default:''" json:"student_grade"`
	StudentBirthday  *time.Time `gorm:"column:student_birthday;type:date" json:"student_birthday"`
	Salesman         string     `gorm:"column:salesman;type:varchar(50);default:''" json:"salesman"`
	Remark           string     `gorm:"column:remark;type:varchar(100);default:''" json:"remark"`
	Source           int8       `gorm:"column:source;type:tinyint;default:0" json:"source"`
	LastPurchasedAt  *time.Time `gorm:"column:last_purchased_at" json:"last_purchased_at"`
	CreatedAt        time.Time  `gorm:"column:created_at" json:"created_at"`
	UpdatedAt        *time.Time `gorm:"column:updated_at" json:"updated_at"`
	CustomerSex      string     `gorm:"column:customer_sex;type:enum('','f','m');default:''" json:"customer_sex"`
	StudentSex       string     `gorm:"column:student_sex;type:enum('','f','m');default:''" json:"student_sex"`
	Type             string     `gorm:"column:type;type:enum('potential','intention','purchased','lost')" json:"type"`
	SubType          string     `gorm:"column:sub_type;type:enum('followed_up','not_follow_up','intention_high','intention_middle','intention_low','intention_unknown','satisfaction_high','satisfaction_middle','satisfaction_low','satisfaction_unknown','returned','competing','non_target')" json:"sub_type"`
	IntentionModelID uint       `gorm:"column:intention_model_id;default:0" json:"intention_model_id"`
	ToStoreStatus    string     `gorm:"column:to_store_status;type:enum('never','one','two','many','unknown');default:unknown" json:"to_store_status"`
	ToStoreTimes     int64      `gorm:"column:to_store_times;type:smallint;default:-1" json:"to_store_times"`
	Manager          uint       `gorm:"column:manager;default:0" json:"manager"`
}

func (Contact) TableName() string {
	return "contact"
}
