package dto

type BugfreeMachine struct {
	Title string `gorm:"column:title"`
	Type  string `gorm:"column:type"`
	Order int    `gorm:"column:order"`
}

type BugfreeTextbookLabel struct {
	Id   int    `gorm:"column:id"`
	Name string `gorm:"column:name"`
}

type BugfreeSubject struct {
	Id   int    `gorm:"column:id"`
	Name string `gorm:"column:name"`
}

type BugfreeStage struct {
	Id   int    `gorm:"column:id"`
	Name string `gorm:"column:name"`
}

type BugfreeGrade struct {
	Id        int    `gorm:"column:id"`
	Name      string `gorm:"column:name"`
	StageId   int    `gorm:"column:stage_id"`
	StageName string `gorm:"column:stage_name"`
}

type BugfreeLabel struct {
	Id   int    `gorm:"column:id"`
	Name string `gorm:"column:name"`
}

type BugfreeNotice struct {
	Data string `gorm:"column:data"`
}
