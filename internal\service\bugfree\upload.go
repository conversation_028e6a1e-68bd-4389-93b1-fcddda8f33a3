package bugfree

import (
	"crypto/rand"
	"math/big"
	"path/filepath"
	"time"

	"marketing-app/internal/pkg/oss"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

// UploadFileService 上传文件服务接口
type UploadFileService interface {
	UploadFile(c *gin.Context, fileData []byte, originalFilename string) (*UploadFileResult, error)
}

// UploadFileResult 上传文件结果
type UploadFileResult struct {
	Name string `json:"name"`
	Mime string `json:"mime"`
	Size int64  `json:"size"`
	URL  string `json:"url"`
}

// uploadFileServiceImpl 上传文件服务实现
type uploadFileServiceImpl struct {
	ossService oss.OSSService
}

// NewUploadFileService 创建上传文件服务实例
func NewUploadFileService(ossService oss.OSSService) UploadFileService {
	return &uploadFileServiceImpl{
		ossService: ossService,
	}
}

// UploadFile 上传文件
func (u *uploadFileServiceImpl) UploadFile(c *gin.Context, fileData []byte, originalFilename string) (*UploadFileResult, error) {
	// 验证文件大小（最小32字节）
	if len(fileData) < 32 {
		return nil, errors.New("文件不能小于32Byte")
	}

	// 生成文件名
	filename, err := u.generateFilename(originalFilename)
	if err != nil {
		return nil, errors.Wrap(err, "生成文件名失败")
	}

	// 构建OSS文件路径
	ossFilename := "rbcare/bugfree/images/" + filename

	// 上传文件到OSS
	err = u.ossService.UploadFile(ossFilename, fileData)
	if err != nil {
		return nil, errors.Wrap(err, "上传文件失败")
	}

	// 获取文件信息
	fileInfo, err := u.ossService.GetFileInfo(ossFilename)
	if err != nil {
		return nil, errors.Wrap(err, "获取文件信息失败")
	}

	// 获取完整URL
	fullURL := u.ossService.GetFullURL(ossFilename)

	// 构建响应结果
	result := &UploadFileResult{
		Name: filename,
		Mime: fileInfo.ContentType, // 从OSS获取的Content-Type
		Size: fileInfo.Size,
		URL:  fullURL,
	}

	return result, nil
}

// generateFilename 生成文件名
func (u *uploadFileServiceImpl) generateFilename(originalFilename string) (string, error) {
	// 获取文件扩展名
	extension := filepath.Ext(originalFilename)
	if extension == "" {
		extension = ".jpg" // 默认扩展名
	}

	// 生成时间戳
	now := time.Now().Format("20060102150405")

	// 生成6位随机字符串
	randomString, err := u.generateRandomString(6)
	if err != nil {
		return "", errors.Wrap(err, "生成随机字符串失败")
	}

	// 组合文件名
	filename := now + randomString + extension
	return filename, nil
}

// generateRandomString 生成随机字符串
func (u *uploadFileServiceImpl) generateRandomString(length int) (string, error) {
	const charset = "abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNOPQRSTUVWXYZ1234567890"
	result := make([]byte, length)

	for i := range result {
		// 生成随机索引
		randomIndex, err := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		if err != nil {
			return "", errors.Wrap(err, "生成随机数失败")
		}
		result[i] = charset[randomIndex.Int64()]
	}

	return string(result), nil
}
