package bugfree

import (
	bugfreeClient "marketing-app/internal/api/client/bugfree"
	"marketing-app/internal/api/client/bugfree/config"
	bugfreeHandler "marketing-app/internal/handler/bugfree"
	"marketing-app/internal/pkg/db"
	"marketing-app/internal/repository"
	bugfreeRepo "marketing-app/internal/repository/bugfree"
	bugfreeService "marketing-app/internal/service/bugfree"

	"github.com/gin-gonic/gin"
)

type BugfreeRouter struct {
	bugfreeHandler bugfreeHandler.Bugfree
}

func NewBugfreeRouter() *BugfreeRouter {
	database, _ := db.GetDB()
	repo := bugfreeRepo.NewBugfreeRepository(database)
	cfg := config.LoadBugfreeConfig()
	client := bugfreeClient.NewBugfreeClient(cfg)
	adminUserRepo := repository.NewAdminUserRepository(database)
	svc := bugfreeService.NewBugfree(repo, client, adminUserRepo)
	handler := bugfreeHandler.NewBugfreeHandler(svc)
	return &BugfreeRouter{
		bugfreeHandler: handler,
	}
}

func (b *BugfreeRouter) Register(rg *gin.RouterGroup) {
	bugfreeGroup := rg.Group("/bugfree")
	{
		bugfreeGroup.GET("/user", b.bugfreeHandler.GetBugfreeUser)                        // 获取问题反馈用户
		bugfreeGroup.GET("/v1/category", b.bugfreeHandler.GetBugFreeCategory)             // 获取问题类别
		bugfreeGroup.GET("/search", b.bugfreeHandler.GetBugfreeSearch)                    // 搜索问题列表
		bugfreeGroup.GET("/v1/info", b.bugfreeHandler.GetBugfreeInfo)                     // 问题详情
		bugfreeGroup.GET("/replies", b.bugfreeHandler.GetBugfreeReplies)                  // 问题回复列表
		bugfreeGroup.POST("/reply", b.bugfreeHandler.PostBugfreeReply)                    // 提交回复
		bugfreeGroup.POST("/duplicate-reply", b.bugfreeHandler.PostBugfreeDuplicateReply) // 重复问题回复
		bugfreeGroup.POST("/repeat", b.bugfreeHandler.PostBugfreeRepeat)                  // 标记重复问题
		bugfreeGroup.GET("/textbook-resource", b.bugfreeHandler.GetTextbookResource)      // 获取教材资源标签
		bugfreeGroup.GET("/subject-grade", b.bugfreeHandler.GetSubjectGrade)              // 获取学科年级信息
		bugfreeGroup.GET("/label", b.bugfreeHandler.GetBugfreeLabel)                      // 获取问题反馈分类标签
		bugfreeGroup.GET("/notice", b.bugfreeHandler.GetBugfreeNotice)                    // 获取反馈须知
		bugfreeGroup.POST("/evaluations", b.bugfreeHandler.PostBugfreeEvaluations)        // 提交问题反馈评价
		bugfreeGroup.POST("/add-v4", b.bugfreeHandler.PostBugfreeAddV4)                   // 终端提交用户反馈（v4）
		bugfreeGroup.POST("/repair-add-v2", b.bugfreeHandler.PostBugfreeRepairAddV2)      // 提交维修反馈（v2）
		bugfreeGroup.POST("/upload-file", b.bugfreeHandler.PostBugfreeUploadFile)         // 上传文件
	}
}
