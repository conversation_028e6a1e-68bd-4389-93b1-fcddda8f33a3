package client

// BugfreeRepairAddV2Content 维修反馈提交内容
type BugfreeRepairAddV2Content struct {
	UserID          int          `json:"user_id" binding:"required"`      // 用户ID
	Model           string       `json:"model" binding:"required"`        // 机型
	ProductID       string       `json:"product_id" binding:"required"`   // 产品ID
	Title           string       `json:"title" binding:"required"`        // 标题
	Description     string       `json:"description" binding:"required"`  // 描述
	Username        string       `json:"username" binding:"required"`     // 用户名
	PhoneNumber     string       `json:"phone_number" binding:"required"` // 手机号
	IMEI            string       `json:"imei"`                            // IMEI码，手表类机型问题必填
	MachineIdentity string       `json:"machine_identity"`                // 机器标识，S/N、序列号或IMEI中的一个，硬件问题必填
	RepeatStep      string       `json:"repeat_step"`                     // 重现步骤，软件问题必填
	SoftwareType    string       `json:"software_type"`                   // 软件问题类型，软件问题必填
	HardwareType    string       `json:"hardware_type"`                   // 硬件问题类型，硬件问题必填
	SuggestionType  string       `json:"suggestion_type"`                 // 意见建议类型，意见建议必填
	Attachments     []Attachment `json:"attachments"`                     // 附件列表
}

// BugfreeRepairAddV2Request 维修反馈提交请求
type BugfreeRepairAddV2Request struct {
	Content  BugfreeRepairAddV2Content `json:"content" binding:"required"` // 反馈内容
	DeviceID int                       `json:"device_id"`                  // 设备ID
}
