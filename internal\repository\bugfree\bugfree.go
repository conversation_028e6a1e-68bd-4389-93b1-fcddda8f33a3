package bugfree

import (
	"fmt"
	"marketing-app/internal/model"
	"marketing-app/internal/repository/bugfree/dto"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type BugfreeRepo interface {
	GetMachineType(c *gin.Context) (data []dto.BugfreeMachine, err error)
	GetTextbookLabels(c *gin.Context) (data []dto.BugfreeTextbookLabel, err error)
	GetSubjects(c *gin.Context) (data []dto.BugfreeSubject, err error)
	GetStages(c *gin.Context) (data []dto.BugfreeStage, err error)
	GetGrades(c *gin.Context) (data []dto.BugfreeGrade, err error)
	GetLabels(c *gin.Context, categoryId int, categoryKey string) (data []dto.BugfreeLabel, err error)
	GetNotice(c *gin.Context) (data string, err error)
	GetMachineByTitle(c *gin.Context, title string) (t string, err error)
}

type Bugfree struct {
	db *gorm.DB
}

func NewBugfreeRepository(db *gorm.DB) BugfreeRepo {
	return &Bugfree{
		db: db,
	}
}

func (b *Bugfree) GetMachineType(c *gin.Context) (data []dto.BugfreeMachine, err error) {
	err = b.db.WithContext(c).Model(&model.BugfreeMachine{}).Order("`order`").Find(&data).Error
	return data, err
}

func (b *Bugfree) GetTextbookLabels(c *gin.Context) (data []dto.BugfreeTextbookLabel, err error) {
	err = b.db.WithContext(c).Model(&model.BugfreeLabel{}).
		Select("id, name").
		Where("visibility = ? AND category = ? AND type = ?", 1, 1, 1).
		Find(&data).Error
	return data, err
}

func (b *Bugfree) GetSubjects(c *gin.Context) (data []dto.BugfreeSubject, err error) {
	err = b.db.WithContext(c).Model(&model.Subject{}).
		Select("id, name").
		Where("visibility = ?", 1).
		Find(&data).Error
	return data, err
}

func (b *Bugfree) GetStages(c *gin.Context) (data []dto.BugfreeStage, err error) {
	err = b.db.WithContext(c).Model(&model.Stage{}).
		Select("id, name").
		Where("visibility = ?", 1).
		Order("`order`").
		Find(&data).Error
	return data, err
}

func (b *Bugfree) GetGrades(c *gin.Context) (data []dto.BugfreeGrade, err error) {
	err = b.db.WithContext(c).Model(&model.Grade{}).
		Select("grade.id, grade.name, grade.stage as stage_id, s.name as stage_name").
		Joins("left join stage s on grade.stage = s.id").
		Where("grade.visibility = ?", 1).
		Order("grade.stage, grade.order").
		Find(&data).Error
	return data, err
}

func (b *Bugfree) GetLabels(c *gin.Context, categoryId int, categoryKey string) (data []dto.BugfreeLabel, err error) {
	if categoryId > 0 {
		// 按分类ID查询
		err = b.db.WithContext(c).Model(&model.BugfreeLabel{}).
			Select("id, name").
			Where("visibility = ? AND category = ? AND type = ?", 1, categoryId, 1).
			Find(&data).Error
	} else if categoryKey != "" {
		// 按分类KEY查询
		err = b.db.WithContext(c).Model(&model.BugfreeLabel{}).
			Select("id, name").
			Where("visibility = ? AND category_key = ? AND type = ?", 1, categoryKey, 1).
			Find(&data).Error
	} else {
		// 参数验证失败
		err = fmt.Errorf("category_id 或 category_key 必须提供其中一个")
	}
	return data, err
}

func (b *Bugfree) GetNotice(c *gin.Context) (data string, err error) {
	var notice dto.BugfreeNotice
	err = b.db.WithContext(c).Model(&model.BugfreeNotice{}).
		Select("data").
		Where("status = ?", 1).
		Limit(1).
		First(&notice).Error

	if err != nil {
		return "", err
	}

	return notice.Data, nil
}

// GetMachineByTitle 根据机型标题获取机型信息
func (b *Bugfree) GetMachineByTitle(c *gin.Context, title string) (t string, err error) {
	var machine model.BugfreeMachine
	err = b.db.WithContext(c).Where("title = ?", title).First(&machine).Error
	if err != nil {
		return "", err
	}
	return machine.Type, nil
}
