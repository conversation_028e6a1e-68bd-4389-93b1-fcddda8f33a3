package model

import "time"

// BugfreeMachine bugfree机型分类
type BugfreeMachine struct {
	ID        int       `gorm:"column:id;primaryKey;autoIncrement" json:"id"`                                                               // 主键
	Order     int       `gorm:"column:order;default:0" json:"order"`                                                                        // 排序
	Title     string    `gorm:"column:title;size:50;not null;default:''" json:"title"`                                                      // 机型
	ParentID  int       `gorm:"column:parent_id;default:0" json:"parent_id"`                                                                // 父类id
	Status    int8      `gorm:"column:status;default:0" json:"status"`                                                                      // 状态，0为不可见，1为可见
	Type      string    `gorm:"column:type;type:enum('tablet','watch','','scanning_pen','study_desk','study_card');default:''" json:"type"` // 机器类型
	CreatedAt time.Time `gorm:"column:created_at;autoUpdateTime" json:"created_at"`                                                         // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`                                                         // 更新时间
}

// TableName 指定表名
func (BugfreeMachine) TableName() string {
	return "bugfree_machine"
}
