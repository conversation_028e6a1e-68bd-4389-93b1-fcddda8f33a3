package utils

import "regexp"

var (
	barcodeRegex      = regexp.MustCompile(`^[A-Za-z0-9]{6,14}$`)
	imeiRegex         = regexp.MustCompile(`^\d{15}$`)
	serialNumberRegex = regexp.MustCompile(`^[A-Za-z0-9]{8,64}$`)
	phoneRegex        = regexp.MustCompile(`^1[0-9]{10}$`)
)

func IsBarcode(code string) bool {
	return barcodeRegex.MatchString(code)
}

func IsImei(code string) bool {
	return imeiRegex.MatchString(code)
}

func IsSerialNumber(code string) bool {
	return serialNumberRegex.MatchString(code)
}

func IsPhone(code string) bool {
	return phoneRegex.MatchString(code)
}

var grades = []string{
	"学前-小班", "学前-中班", "学前-大班",
	"小学-一年级", "小学-二年级", "小学-三年级", "小学-四年级", "小学-五年级", "小学-六年级",
	"中学-六年级", "中学-七年级", "中学-八年级", "中学-九年级",
	"高中-高一", "高中-高二", "高中-高三",
}

// IsGradeValid 判断是否在grades中
func IsGradeValid(gradeName string) bool {
	for _, g := range grades {
		if g == gradeName {
			return true
		}
	}
	return false
}
